## R Analytics Microservice Integration

### Request/Response Contracts

- POST /v1/bp/compute/day
  - Request: { patient_id, day, analysis_version, readings: [{ sbp, dbp, pulse, reading_time }] }
  - Response: {
      success, analysis_version, computed_at, last_input_at, total_readings,
      basic_stats, variability_stats, range_stats, pressure_stats, time_of_day,
      classification_counts, latest_reading, series
    }

- POST /v1/bp/compute/duration
  - Request: { patient_id, duration, analysis_version, readings: [...] }
  - Response: same shape as compute/day

- POST /v1/bp/compute-durations (bootstrap placeholder)
  - Request: { patientId, windows: [ '1Month', ... ] }
  - Response: { patientId, windows, metrics: { [window]: { ... } } }

### Environment Variables (Backend)

- FEATURE_R_ANALYTICS=true
- CURRENT_ANALYSIS_VERSION=v1.0.0-r
- R_ANALYTICS_BASE_URL=http://localhost:8000
- R_ANALYTICS_TOKEN=SYQEsz{3swa5]8?|.AjT?HYQ4ro.2T
- R_ANALYTICS_TIMEOUT_MS=15000
- R_ANALYTICS_RETRY_COUNT=2
- R_ANALYTICS_RETRY_BACKOFF_MS=500
- R_ANALYTICS_CIRCUIT_OPEN_THRESHOLD=5
- R_ANALYTICS_CIRCUIT_RESET_MS=60000
- R_ANALYTICS_REQUIRED=true           # R-only mode (fallback removed in code); if false, this setting is ignored
- FEATURE_BP_ON_WRITE=true
- BP_ON_WRITE_DEBOUNCE_MS=15000
- BP_ROLLOUT_MODE=all                # 'all' or 'canary'
- BP_CANARY_IMEIS=352847091234567    # comma-separated IMEIs if canary

### Operational Notes

- On-write processing: ingestion emits an event; processing is debounced per IMEI and runs R-first, Node-fallback.
- Security: R requires Authorization Bearer token; backend sends `R_ANALYTICS_TOKEN`.
- Observability: correlation ID header (REQUEST_CORRELATION_HEADER) propagated to R; logs record start/completion and failures.
- Rollout: set `BP_ROLLOUT_MODE=canary` and `BP_CANARY_IMEIS=<list>` to limit analytics to a subset; switch to `all` when ready.

### Cleanup (Phase 8)

- Deprecated: `utils/analyticsClient.mjs` removed in favor of `utils/rAnalyticsClient.mjs`.
- Analysis calls centralized through `rAnalyticsClient.mjs`; Node-only analyzer remains as a fallback only.

# Cardiowell Healthcare Platform Documentation

## 1. Project Overview

Cardiowell is a comprehensive healthcare platform designed to facilitate remote patient monitoring, clinical data management, and streamlined communication between healthcare providers and patients. The platform serves as a bridge between medical devices, healthcare professionals, and patients, enabling real-time health monitoring and data-driven care decisions.

The system consists of three distinct portals:

- **Admin Portal**: Provides administrative capabilities for managing clinics, programs, devices, and system users. Administrators can configure system-wide settings, manage device inventory, and oversee clinic operations.

- **Provider Portal**: Enables healthcare providers to monitor patient health data, review measurements, set thresholds for alerts, create clinical notes, and communicate with patients. Providers can access comprehensive dashboards showing patient metrics and receive notifications when readings fall outside established parameters.

- **Patient Portal**: Allows patients to view their health data, communicate with providers, manage connected devices, and track their progress over time. The patient interface is designed to be intuitive and accessible, promoting patient engagement in their healthcare journey.

The core functionality revolves around integrating data from various medical devices (blood pressure monitors, glucose meters, pulse oximeters, weight scales), processing and analyzing this data, and presenting actionable insights to both providers and patients.

## 2. Architecture Overview

### Frontend Architecture (React)

The frontend is built using React and organized into modular components following a feature-based structure:

- **Components**: Reusable UI elements including charts, forms, modals, and data displays
- **Services**: API interaction layer that communicates with the backend
- **Utils**: Helper functions for data manipulation, formatting, and calculations
- **Helpers**: Utility functions for specific tasks like routing and time parsing
- **Common**: Shared constants, colors, and conversion functions
- **Images/Icons**: Visual assets used throughout the application

Key frontend architectural patterns:
- Component-based architecture with reusable UI elements
- State management through React hooks and context
- Client-side routing for navigation between different views
- Form handling and validation for data entry
- Data visualization through charting libraries

### Backend Architecture (Node.js/Express)

The backend follows a modular architecture with clear separation of concerns:

- **Controllers**: Handle HTTP requests and responses, input validation, and route logic
- **Services**: Contain business logic, data processing, and interaction with the database
- **Models**: Define database schemas and document structures
- **Routes**: Define API endpoints and map them to controllers
- **Utils**: Helper functions and shared utilities
- **Auth**: Authentication and authorization mechanisms

The backend architecture implements:
- RESTful API design principles
- MVC-inspired separation of concerns
- Middleware for cross-cutting concerns like authentication and logging
- Integration with external device APIs and services

### System Interaction

The frontend and backend interact primarily through RESTful API calls. The frontend sends HTTP requests to specific endpoints on the backend, which processes these requests, interacts with the database or external services as needed, and returns appropriate responses.

For real-time updates (like device readings or alerts), the system utilizes webhooks and possibly Socket.IO for bidirectional communication between the server and client applications.

## 3. Technology Stack

### Backend Technologies

- **Node.js**: Server-side JavaScript runtime environment
- **Express.js**: Web application framework for handling HTTP requests and routing
- **MongoDB**: NoSQL database for storing patient data, device readings, clinical notes, and system configuration
- **JWT (JSON Web Tokens)**: For secure authentication and session management
- **ESM (ECMAScript Modules)**: Modern JavaScript module system (evident from .mjs file extensions)
- **Encryption Libraries**: For securing sensitive patient health information in clinical notes
- **Webhook Handlers**: For receiving real-time data from connected devices
- **API Integrations**: For communicating with third-party device platforms

### Frontend Technologies

- **React**: JavaScript library for building user interfaces
- **Material-UI**: Component library for implementing design system
- **Chart.js/D3.js**: Data visualization libraries for displaying health metrics
- **React Router**: Client-side routing solution
- **Form Libraries**: For handling form validation and submission
- **PDF Generation**: For creating reports and patient summaries

### DevOps & Infrastructure

- **Docker**: Application containerization (evident from docker-compose.yaml)
- **Heroku**: Cloud platform for application deployment
- **ESLint**: Code quality and style enforcement
- **Jest**: Testing framework for unit and integration tests
- **Sentry**: Error monitoring and reporting

## 4. Key Features

### Patient Management

- **Patient Registration and Onboarding**: Comprehensive patient creation process with demographic and medical information
- **Patient Profile Management**: Tools for updating patient information, contact details, and care preferences
- **Patient Assignment**: Association of patients with specific clinics, programs, and providers

Implementation: `/users/controller/addPatient.mjs`, `/users/controller/editPatient.mjs`, `/users/service/createPatient.mjs`

### Device Management

- **Device Registration**: Adding devices to the system inventory with unique identifiers
- **Device Assignment**: Linking devices to specific patients
- **Bulk Device Operations**: Support for adding multiple devices simultaneously
- **Device Status Monitoring**: Tracking device connectivity, battery levels, and operational status

Implementation: `/device/controller/createDevice.mjs`, `/device/controller/bulkCreateDevices.mjs`, `/deviceUpdates/controller/deviceStatus.mjs`

### Measurement Collection and Analysis

- **Real-time Data Collection**: Gathering measurements from connected devices
- **Historical Data Analysis**: Tracking trends and patterns in patient measurements over time
- **Alert Generation**: Identifying measurements outside of normal ranges and alerting providers
- **Measurement Categorization**: Organizing readings by type (blood pressure, glucose, weight, etc.)

Implementation: `/deviceUpdates/controller/deviceMeasures.mjs`, `/users/service/bodytrace/bodyTraceBpm.mjs`, `/users/service/transtek/transtekGlucose.mjs`

### Clinical Notes

- **Secure Note Creation**: Encrypted documentation of patient encounters and observations
- **Note Retrieval**: Secure access to patient notes for authorized providers
- **Structured Data Capture**: Standardized formats for clinical documentation

Implementation: `/clinicalNotes/controller/createClinicalNote.mjs`, `/clinicalNotes/service/encryption.mjs`, `/clinicalNotes/controller/getClinicalNotes.mjs`

### Program Management

- **Program Creation**: Establishing care programs with specific monitoring parameters
- **Threshold Configuration**: Setting alert thresholds for different measurements
- **Program Assignment**: Associating patients with appropriate care programs

Implementation: `/program/controller/createProgramAndThreshold.mjs`, `/threshold/controller/createThreshold.mjs`, `/threshold/controller/updatePatientThreshold.mjs`

### Clinic Management

- **Clinic Registration**: Adding healthcare facilities to the system
- **Provider Assignment**: Associating healthcare providers with specific clinics
- **Program Configuration**: Setting up programs available at each clinic

Implementation: `/clinic/controller/addClinic.mjs`, `/provider/controller/getProvidersByClinic.mjs`

### Reporting and Analytics

- **Data Visualization**: Charts and graphs showing patient progress over time
- **PDF Report Generation**: Creation of downloadable patient reports
- **Provider Dashboards**: Comprehensive views of patient populations and key metrics

Implementation: `/components/Chart.js`, `/reports/generatePdf.js`, `/components/AdminDashboard.js`

### Authentication and Authorization

- **User Authentication**: Secure login process with JWT token generation
- **Role-based Access Control**: Different permissions for admins, providers, and patients
- **Magic Links**: Passwordless authentication options for enhanced security
- **Short URL Generation**: Creating compact URLs for authentication links

Implementation: `/auth/token.mjs`, `/auth/shortUrls.mjs`, `/utils/generate-magic-link.mjs`

### Patient Assistant

- **Messaging System**: Communication between patients and providers
- **Thread Management**: Organization of conversations by topic
- **Notification System**: Alerts for new messages and important updates

Implementation: `/patient-assistant/assistant.mjs`, `/patient-assistant/messages.collection.mjs`

## 5. Data Flow

### Patient Data Flow

1. **Data Collection**: Health measurements are captured by medical devices (blood pressure monitors, glucose meters, etc.)
2. **Device Transmission**: Devices send data to their respective manufacturer clouds (Withings, iGlucose, etc.)
3. **Backend Integration**: Manufacturer APIs send data to Cardiowell backend via webhooks or API calls
4. **Data Processing**: Readings are processed, categorized, and stored in the database
5. **Threshold Comparison**: Values are compared against patient-specific thresholds
6. **Alert Generation**: Notifications are sent to providers if readings are outside acceptable ranges
7. **Data Presentation**: Processed data is made available to frontend applications for display to providers and patients

### Authentication Flow

1. **User Login**: Credentials are submitted to authentication endpoints
2. **Token Generation**: JWT tokens are created upon successful authentication
3. **Authorization Check**: User permissions determine accessible features and data
4. **Token Refresh**: Session management ensures secure, persistent access

### External Integration Flow

#### Withings Integration

1. **User Authorization**: OAuth flow for connecting patient Withings accounts
2. **Data Synchronization**: Regular polling or webhook reception of new measurements
3. **Data Normalization**: Converting Withings-specific formats to system standards
4. **Storage and Analysis**: Integrating Withings data with other patient measurements

Implementation: `/withings/controller/activate.mjs`, `/withings/service/getAccessToken.mjs`, `/withings/service/getMeas.mjs`

#### Other Device Integrations

Similar flows exist for other device manufacturers (A&D, BerryMed, BodyTrace, Transtek), each with specific API requirements and data formats.

## 6. Device Integrations

### A&D Medical Devices

- **Integration Protocol**: Proprietary API with webhook notifications
- **Data Format**: JSON payloads with standardized measurement fields
- **Device Types**: Blood pressure monitors with systolic, diastolic, and pulse readings
- **Communication Flow**: Bidirectional with device configuration capabilities

Implementation: `/device/service/ad/addForwardTime.mjs`, `/users/service/ad/adBpm.mjs`

### BerryMed Devices

- **Integration Protocol**: Bluetooth to gateway, gateway to cloud, cloud to Cardiowell API
- **Data Format**: Structured JSON with timestamp, device ID, and measurement values
- **Device Types**: Pulse oximeters measuring SpO2 and pulse rate
- **Time Parsing**: Special handling for BerryMed timestamp formats

Implementation: `/device/service/berry/berryLatestMessages.mjs`, `/users/service/berry/pulseOximeter.mjs`, `/helpers/parceBerryMedOximeterTime.js`

### BodyTrace Devices

- **Integration Protocol**: Cellular network to BodyTrace cloud, webhooks to Cardiowell
- **Data Format**: Proprietary JSON structure with device telemetry and measurements
- **Device Types**: Weight scales and blood pressure monitors
- **Handling Multiple Reading Types**: Separate processors for different measurement types

Implementation: `/device/service/bodyTrace/bodyTraceLatestMessages.mjs`, `/users/service/bodytrace/bodyTraceBpm.mjs`, `/models/bodyTraceMessage.mjs`

### Transtek Devices

- **Integration Protocol**: Multiple protocols depending on device type (Bluetooth, cellular)
- **Data Format**: Standardized measurement objects with device metadata
- **Device Types**: Blood glucose meters, blood pressure monitors, weight scales
- **Message Handling**: Specialized processors for different device categories

Implementation: `/device/service/transtek/handleBloodGlucoseMeterMessage.mjs`, `/device/service/transtek/handleSphygmomanometerMessage.mjs`, `/users/service/transtek/transtekBpm.mjs`

### Withings Devices

- **Integration Protocol**: OAuth 2.0 authorization with API access
- **Data Format**: Standardized Withings API responses with measurement groups
- **Device Types**: Smart scales, blood pressure monitors, sleep trackers
- **Authentication Flow**: Complex token management with refresh capabilities
- **Webhook Processing**: Real-time data updates via configured endpoints

Implementation: `/withings/controller/activate.mjs`, `/withings/service/getAccessToken.mjs`, `/withings/utils/categories.mjs`

## 7. Authentication & Security

### Authentication Mechanisms

- **JWT Token Authentication**: Secure, stateless authentication using signed tokens
- **Role-Based Access Control**: Different permission levels for admins, providers, and patients
- **Magic Link Authentication**: Passwordless authentication via secure email links
- **Short URL Generation**: Creating compact, secure URLs for authentication purposes
- **Token Refreshing**: Maintaining secure sessions without frequent re-authentication

Implementation: `/auth/token.mjs`, `/routes/auth.js`, `/utils/generate-magic-link.mjs`

### Security Features

- **Clinical Notes Encryption**: End-to-end encryption of sensitive patient notes
- **Secure API Design**: Protection against common vulnerabilities (CSRF, XSS, injection)
- **Data Sanitization**: Input validation and cleaning to prevent malicious data
- **Sensitive Data Handling**: Special procedures for managing protected health information
- **Access Controls**: Strict limitations on who can access which patient data

Implementation: `/clinicalNotes/service/encryption.mjs`, `/auth/token.mjs`

### Compliance Considerations

- **HIPAA Compliance**: Features designed to maintain patient data privacy
- **Data Retention Policies**: Controlled storage and deletion of patient information
- **Audit Logging**: Tracking access to sensitive patient information

## 8. Database Schema

### Core Entities

#### Patient
- Basic demographic information
- Contact details
- Associated clinic and provider
- Assigned devices
- Care program enrollment
- Custom thresholds
- Measurement history references

#### Device
- Unique identifiers (serial number, IMEI)
- Device type and manufacturer
- Current assignment status
- Calibration and configuration data
- Connection and battery status
- Associated patient (if assigned)

#### Clinical Notes
- Encrypted content
- Author (provider)
- Associated patient
- Creation and modification timestamps
- Categorization metadata

#### Program
- Name and description
- Associated clinic
- Default threshold values
- Enrolled patients
- Active status indicator

#### Threshold
- Measurement type
- Upper and lower bounds
- Associated program or patient
- Override indicators (patient-specific vs. program default)
- Creation and modification information

#### Clinic
- Name and contact information
- Associated providers
- Available programs
- Administrative settings

#### Provider
- Professional credentials
- Clinic associations
- Patient assignments
- Contact information
- Account permissions

### Entity Relationships

- Patients belong to a specific clinic and are assigned to providers
- Devices are manufactured by specific companies and may be assigned to patients
- Clinical notes are created by providers for specific patients
- Programs are created within clinics and have default thresholds
- Patients enroll in programs but may have custom thresholds
- Measurements are associated with specific devices and patients

## 9. Deployment Infrastructure

### Development Environment

- Local Docker containers for consistent development
- Environment variable configuration for service connections
- Local proxies for frontend-backend communication during development

### Testing Infrastructure

- Automated unit tests for critical components
- Test data generation utilities
- Mock device data simulators

Implementation: `/device/service/test/generateSphygmomanometerHeartbeat.mjs`, `/scripts/testDataLoader.mjs`

### Production Deployment

- **Heroku Platform**: Main hosting environment for both frontend and backend
- **Environment Configuration**: Separate variables for production settings
- **Database Provisioning**: MongoDB Atlas for production data storage
- **API Gateway**: Managing external API requests and rate limiting
- **SSL/TLS**: Secure communication with encryption
- **Monitoring**: Sentry integration for error tracking and performance monitoring

### Continuous Integration/Deployment

- Automated build processes
- Deployment pipelines from source control to production
- Version management and rollback capabilities

### Scaling Considerations

- Horizontal scaling for increased user load
- Database sharding for large patient populations
- Caching strategies for frequently accessed data

---

This documentation provides a comprehensive overview of the Cardiowell healthcare platform, detailing its architecture, features, and technical implementation. The system's modular design allows for continued expansion of supported devices, features, and integrations while maintaining security, performance, and regulatory compliance. 