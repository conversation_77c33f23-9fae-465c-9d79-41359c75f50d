# BP Metrics – Phase 0 Specification (POC)

## Goal
Compute and display BodyTrace Cardiowell BP statistics on demand when a provider opens a patient dashboard (DEV only for now). No DB schema changes. Optionally add a cache later (out of scope here).

## Scope (Phase 1 metrics)
- totals:
  - totalReadings
  - daysWithReadings
  - firstReadingAt, lastReadingAt
- aggregates:
  - avgSys, avgDia, avgPulse
- extremes:
  - maxSys, maxSysAt
  - minSys, minSysAt
  - maxDia, maxDiaAt
  - minDia, minDiaAt
- stageDistribution (percentages of readings):
  - normal, elevated, stage1, stage2, crisis (thresholds below)
- timeOfDay split (configurable):
  - morning.avgSys/avgDia, evening.avgSys/avgDia
- metadata:
  - rangeStart, rangeEnd, tzApplied, requestTime, lastReadingTsIncluded, version

## Inputs
- Source: Mongo `ad_bpms` (BodyTrace) – fields: `payload.imei`, `payload.sys`, `payload.dia`, `payload.pulse`, `payload.timestamp` (ISO or epoch ms)
- Query parameters:
  - start: YYYY-MM-DD (optional)
  - end: YYYY-MM-DD (optional)
  - tz: IANA timezone string (optional; fallback order: patient -> clinic -> UTC)
  - If start/end not provided: default window = last 90 days

## Normalization & rules
- Unit normalization: BodyTrace mmHg values are scaled ×100; divide by 100
- Timestamp parsing: accept ISO string or epoch ms; convert to `tz` for day bucketing
- Deduplication: drop duplicates by hash(imei, ts, sys, dia)
- Outliers (ignore reading if):
  - dia < 40 or dia > 140
  - sys < 70 or sys > 260
  - dia >= sys
- Time-of-day buckets:
  - morning: 04:00–11:59
  - evening: 16:00–23:59
- Staging thresholds (AHA-aligned):
  - normal: sys < 120 and dia < 80
  - elevated: 120 ≤ sys ≤ 129 and dia < 80
  - stage1: 130–139 or 80–89
  - stage2: 140–179 or 90–119
  - crisis: sys ≥ 180 or dia ≥ 120

## API contract (backend)
- GET `/api/bp-metrics/:imei?start=YYYY-MM-DD&end=YYYY-MM-DD&tz=America/New_York`
- Auth: provider must have access to patient/clinic (reuse existing middleware)
- Response (example keys):
```json
{
  "success": true,
  "requestTime": "2025-10-01T12:34:56.789Z",
  "computedForRange": { "start": "2025-07-03", "end": "2025-10-01" },
  "tzApplied": "America/New_York",
  "dataAvailable": true,
  "data": {
    "totals": { "totalReadings": 28, "daysWithReadings": 10, "firstReadingAt": "...", "lastReadingAt": "..." },
    "aggregates": { "avgSys": 123, "avgDia": 78, "avgPulse": 72 },
    "extremes": { "maxSys": 152, "maxSysAt": "...", "minSys": 104, "minSysAt": "...", "maxDia": 96, "maxDiaAt": "...", "minDia": 62, "minDiaAt": "..." },
    "stagesPct": { "normal": 35.7, "elevated": 21.4, "stage1": 28.6, "stage2": 14.3, "crisis": 0 },
    "timeOfDay": { "morning": { "avgSys": 121, "avgDia": 77 }, "evening": { "avgSys": 126, "avgDia": 79 } }
  },
  "lastReadingTsIncluded": 1696012345678,
  "version": "1.0.0"
}
```
- Error cases:
  - 200 with `{ success: true, dataAvailable: false }` when no readings in range
  - 400 for invalid params; 403 for unauthorized; 500 for server errors

## Frontend consumption (dashboard)
- Where: `patientCareReact-master/src/components/PatientData/Dashboard.jsx`
- When BP section is visible and IMEI present:
  - Call the endpoint with the selected duration (1, 3, 6 months, 1 year, all)
  - Render panel with: totalReadings, daysWithReadings, Avg BP, and optional stage distribution summary
  - Graceful empty/error states; no client persistence

## Performance targets
- P95 latency < 500 ms for ≤ 90 days and ≤ 1k readings
- Cap maximum window to 365 days for POC

## Acceptance criteria
- Opening a dashboard triggers compute and returns the structure above
- Panel displays values consistent with manual calculation on a sample patient
- No DB schema changes required; read-only access to existing collections
- Logs contain compute duration and count of readings processed

## Validation/test fixtures
- Use `device/service/testDataUploads/loadBloodPressureBodyTraceData.mjs` to seed BodyTrace data from CSV
- Prepare 3 fixtures (IMEI or CSV) covering typical, sparse, and outlier-heavy data
- Verify:
  - averages/extremes with hand-checked calculations
  - staging percentages sum to ~100%
  - time-of-day split respects tz and bucket windows
