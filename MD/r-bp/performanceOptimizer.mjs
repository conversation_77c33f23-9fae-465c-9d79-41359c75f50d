import { getRBPDB } from './mongoConnection.mjs';

/**
 * Performance Optimizer - Phase 5 Implementation
 * 
 * Caching, query optimization, and performance monitoring
 * for BP duration metrics system
 */

// In-memory cache for frequently accessed data
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 1000; // Maximum number of cached items

// Performance monitoring
const performanceMetrics = {
  cacheHits: 0,
  cacheMisses: 0,
  queryTimes: [],
  averageQueryTime: 0,
  totalQueries: 0
};

/**
 * Cache entry structure
 */
class CacheEntry {
  constructor(data, ttl = CACHE_TTL) {
    this.data = data;
    this.expiresAt = Date.now() + ttl;
    this.accessCount = 0;
    this.lastAccessed = Date.now();
  }
  
  isExpired() {
    return Date.now() > this.expiresAt;
  }
  
  isValid() {
    return !this.isExpired();
  }
  
  touch() {
    this.accessCount++;
    this.lastAccessed = Date.now();
  }
}

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
export const getCacheStats = () => {
  const now = Date.now();
  const entries = Array.from(cache.values());
  const validEntries = entries.filter(entry => entry.isValid());
  const expiredEntries = entries.filter(entry => entry.isExpired());
  
  return {
    totalEntries: cache.size,
    validEntries: validEntries.length,
    expiredEntries: expiredEntries.length,
    hitRate: performanceMetrics.totalQueries > 0 ? 
      (performanceMetrics.cacheHits / performanceMetrics.totalQueries * 100).toFixed(2) + '%' : '0%',
    averageQueryTime: performanceMetrics.averageQueryTime.toFixed(2) + 'ms',
    memoryUsage: process.memoryUsage()
  };
};

/**
 * Clear expired cache entries
 */
const clearExpiredCache = () => {
  const now = Date.now();
  let cleared = 0;
  
  for (const [key, entry] of cache.entries()) {
    if (entry.isExpired()) {
      cache.delete(key);
      cleared++;
    }
  }
  
  if (cleared > 0) {
    console.log(`🧹 Cleared ${cleared} expired cache entries`);
  }
};

/**
 * Evict least recently used entries if cache is full
 */
const evictLRU = () => {
  if (cache.size < MAX_CACHE_SIZE) return;
  
  const entries = Array.from(cache.entries());
  entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
  
  const toEvict = entries.slice(0, Math.floor(MAX_CACHE_SIZE * 0.2)); // Evict 20%
  
  for (const [key] of toEvict) {
    cache.delete(key);
  }
  
  console.log(`🗑️ Evicted ${toEvict.length} LRU cache entries`);
};

/**
 * Get data from cache
 * @param {string} key - Cache key
 * @returns {Object|null} Cached data or null if not found/expired
 */
const getFromCache = (key) => {
  const entry = cache.get(key);
  
  if (!entry) {
    performanceMetrics.cacheMisses++;
    return null;
  }
  
  if (entry.isExpired()) {
    cache.delete(key);
    performanceMetrics.cacheMisses++;
    return null;
  }
  
  entry.touch();
  performanceMetrics.cacheHits++;
  return entry.data;
};

/**
 * Store data in cache
 * @param {string} key - Cache key
 * @param {Object} data - Data to cache
 * @param {number} ttl - Time to live in milliseconds
 */
const setCache = (key, data, ttl = CACHE_TTL) => {
  // Clear expired entries first
  clearExpiredCache();
  
  // Evict LRU if needed
  evictLRU();
  
  cache.set(key, new CacheEntry(data, ttl));
};

/**
 * Generate cache key for duration metrics
 * @param {string} patientId - Patient ID
 * @param {string} duration - Duration (optional)
 * @returns {string} Cache key
 */
const generateCacheKey = (patientId, duration = null) => {
  return duration ? `metrics:${patientId}:${duration}` : `metrics:${patientId}:all`;
};

/**
 * Get duration metrics with caching
 * @param {string} patientId - Patient ID
 * @param {string} duration - Specific duration (optional)
 * @returns {Object} Duration metrics
 */
export const getCachedDurationMetrics = async (patientId, duration = null) => {
  const startTime = Date.now();
  const cacheKey = generateCacheKey(patientId, duration);
  
  try {
    // Try cache first
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log(`📦 Cache hit for ${cacheKey}`);
      return cachedData;
    }
    
    console.log(`📦 Cache miss for ${cacheKey}, fetching from database`);
    
    // Fetch from database
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    let query = { patientId };
    let projection = {};
    
    if (duration) {
      projection[`metrics${duration}`] = 1;
      projection.lastUpdated = 1;
      projection.processingStats = 1;
    }
    
    const metricsDoc = await collection.findOne(query, { projection });
    
    if (!metricsDoc) {
      const result = {
        patientId,
        lastUpdated: null,
        durations: {},
        healthStatus: 'no_data',
        message: 'No duration metrics available'
      };
      
      // Cache the "no data" result for shorter time
      setCache(cacheKey, result, 60000); // 1 minute
      return result;
    }
    
    // Process the data
    let result;
    if (duration) {
      const metrics = metricsDoc[`metrics${duration}`];
      result = {
        patientId,
        lastUpdated: metricsDoc.lastUpdated,
        duration: duration,
        metrics: metrics || null,
        healthStatus: metrics ? 'healthy' : 'no_data',
        processingStats: metricsDoc.processingStats
      };
    } else {
      const durations = ['1Month', '3Months', '6Months', '1Year', '2Years', 'All'];
      const durationData = {};
      
      for (const dur of durations) {
        durationData[dur] = metricsDoc[`metrics${dur}`] || null;
      }
      
      result = {
        patientId,
        lastUpdated: metricsDoc.lastUpdated,
        durations: durationData,
        healthStatus: 'healthy',
        processingStats: metricsDoc.processingStats
      };
    }
    
    // Cache the result
    setCache(cacheKey, result);
    
    // Update performance metrics
    const queryTime = Date.now() - startTime;
    performanceMetrics.queryTimes.push(queryTime);
    performanceMetrics.totalQueries++;
    
    // Keep only last 100 query times for average calculation
    if (performanceMetrics.queryTimes.length > 100) {
      performanceMetrics.queryTimes = performanceMetrics.queryTimes.slice(-100);
    }
    
    performanceMetrics.averageQueryTime = 
      performanceMetrics.queryTimes.reduce((a, b) => a + b, 0) / performanceMetrics.queryTimes.length;
    
    console.log(`📊 Query completed in ${queryTime}ms`);
    return result;
    
  } catch (error) {
    console.error(`❌ Error getting cached duration metrics:`, error);
    throw error;
  }
};

/**
 * Invalidate cache for a patient
 * @param {string} patientId - Patient ID
 * @param {string} duration - Specific duration (optional)
 */
export const invalidateCache = (patientId, duration = null) => {
  if (duration) {
    const key = generateCacheKey(patientId, duration);
    cache.delete(key);
    console.log(`🗑️ Invalidated cache for ${key}`);
  } else {
    // Invalidate all caches for this patient
    const keysToDelete = [];
    for (const key of cache.keys()) {
      if (key.startsWith(`metrics:${patientId}:`)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      cache.delete(key);
    }
    
    console.log(`🗑️ Invalidated ${keysToDelete.length} cache entries for patient ${patientId}`);
  }
};

/**
 * Clear all cache
 */
export const clearAllCache = () => {
  const size = cache.size;
  cache.clear();
  console.log(`🧹 Cleared all cache (${size} entries)`);
};

/**
 * Get optimized query for duration metrics
 * @param {string} patientId - Patient ID
 * @param {Array} durations - Array of durations to fetch
 * @returns {Object} Optimized MongoDB query
 */
export const getOptimizedQuery = (patientId, durations = null) => {
  const query = { patientId };
  
  if (durations && durations.length > 0) {
    const projection = { patientId: 1, lastUpdated: 1, processingStats: 1 };
    
    for (const duration of durations) {
      projection[`metrics${duration}`] = 1;
    }
    
    return { query, projection };
  }
  
  return { query };
};

/**
 * Batch get duration metrics for multiple patients
 * @param {Array} patientIds - Array of patient IDs
 * @param {Array} durations - Array of durations to fetch
 * @returns {Object} Batch results
 */
export const batchGetDurationMetrics = async (patientIds, durations = null) => {
  const startTime = Date.now();
  
  try {
    console.log(`📦 Batch fetching metrics for ${patientIds.length} patients`);
    
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    const query = { patientId: { $in: patientIds } };
    const { projection } = getOptimizedQuery(null, durations);
    
    const results = await collection.find(query, { projection }).toArray();
    
    // Process results
    const processedResults = {};
    for (const doc of results) {
      processedResults[doc.patientId] = doc;
    }
    
    // Fill in missing patients
    for (const patientId of patientIds) {
      if (!processedResults[patientId]) {
        processedResults[patientId] = {
          patientId,
          lastUpdated: null,
          durations: {},
          healthStatus: 'no_data'
        };
      }
    }
    
    const queryTime = Date.now() - startTime;
    console.log(`📊 Batch query completed in ${queryTime}ms`);
    
    return {
      results: processedResults,
      queryTime,
      patientCount: patientIds.length,
      foundCount: results.length
    };
    
  } catch (error) {
    console.error(`❌ Error in batch get duration metrics:`, error);
    throw error;
  }
};

/**
 * Get performance metrics
 * @returns {Object} Performance metrics
 */
export const getPerformanceMetrics = () => {
  return {
    ...performanceMetrics,
    cacheStats: getCacheStats(),
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage()
  };
};

/**
 * Reset performance metrics
 */
export const resetPerformanceMetrics = () => {
  performanceMetrics.cacheHits = 0;
  performanceMetrics.cacheMisses = 0;
  performanceMetrics.queryTimes = [];
  performanceMetrics.averageQueryTime = 0;
  performanceMetrics.totalQueries = 0;
  
  console.log(`🔄 Performance metrics reset`);
};

/**
 * Warm up cache with frequently accessed data
 * @param {Array} patientIds - Array of patient IDs to warm up
 * @param {Array} durations - Array of durations to warm up
 */
export const warmUpCache = async (patientIds, durations = null) => {
  console.log(`🔥 Warming up cache for ${patientIds.length} patients`);
  
  const startTime = Date.now();
  let warmedUp = 0;
  
  try {
    for (const patientId of patientIds) {
      try {
        await getCachedDurationMetrics(patientId, durations);
        warmedUp++;
      } catch (error) {
        console.error(`❌ Error warming up cache for ${patientId}:`, error);
      }
    }
    
    const warmupTime = Date.now() - startTime;
    console.log(`🔥 Cache warmup completed: ${warmedUp}/${patientIds.length} patients in ${warmupTime}ms`);
    
    return {
      warmedUp,
      totalPatients: patientIds.length,
      warmupTime,
      successRate: (warmedUp / patientIds.length * 100).toFixed(2) + '%'
    };
    
  } catch (error) {
    console.error(`❌ Error during cache warmup:`, error);
    throw error;
  }
};

/**
 * Monitor cache health and perform maintenance
 */
export const performCacheMaintenance = () => {
  console.log(`🔧 Performing cache maintenance`);
  
  const stats = getCacheStats();
  console.log(`📊 Cache stats: ${stats.validEntries}/${stats.totalEntries} valid entries`);
  console.log(`📈 Hit rate: ${stats.hitRate}`);
  console.log(`⏱️ Average query time: ${stats.averageQueryTime}`);
  
  // Clear expired entries
  clearExpiredCache();
  
  // If hit rate is low, consider clearing more cache
  const hitRate = parseFloat(stats.hitRate);
  if (hitRate < 50 && stats.validEntries > 100) {
    console.log(`⚠️ Low hit rate (${stats.hitRate}), clearing additional cache`);
    evictLRU();
  }
  
  console.log(`✅ Cache maintenance completed`);
};

// Set up periodic cache maintenance
setInterval(performCacheMaintenance, 10 * 60 * 1000); // Every 10 minutes

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(`\n🛑 Shutting down performance optimizer...`);
  clearAllCache();
  console.log(`✅ Performance optimizer shutdown complete`);
});

process.on('SIGTERM', () => {
  console.log(`\n🛑 Shutting down performance optimizer...`);
  clearAllCache();
  console.log(`✅ Performance optimizer shutdown complete`);
});






