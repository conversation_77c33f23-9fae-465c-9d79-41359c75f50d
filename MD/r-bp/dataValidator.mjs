import { getRBPDB } from './mongoConnection.mjs';

/**
 * Data Validator - Phase 5 Implementation
 * 
 * Comprehensive data validation for BP duration metrics
 * Ensures data integrity before storage and provides fallback mechanisms
 */

/**
 * Validate R analysis result structure
 * @param {Object} analysisResult - Result from R analysis
 * @param {string} duration - Duration being validated
 * @returns {Object} Validation result with success flag and errors
 */
export const validateRAnalysisResult = (analysisResult, duration) => {
  const errors = [];
  const warnings = [];
  
  // Check if result exists
  if (!analysisResult) {
    errors.push('Analysis result is null or undefined');
    return { isValid: false, errors, warnings };
  }
  
  // Check success flag
  if (analysisResult.success === false) {
    errors.push(`R analysis failed: ${analysisResult.error || 'Unknown error'}`);
    return { isValid: false, errors, warnings };
  }
  
  // Required fields validation
  const requiredFields = [
    'total_readings',
    'basic_stats',
    'variability_stats',
    'range_stats',
    'pressure_stats',
    'circadian_stats',
    'time_of_day',
    'classification_counts',
    'alerts',
    'combined_stats'
  ];
  
  for (const field of requiredFields) {
    if (!analysisResult[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }
  
  // Validate basic stats structure
  if (analysisResult.basic_stats) {
    const basicStatsFields = ['mean_sbp', 'mean_dbp', 'map'];
    for (const field of basicStatsFields) {
      if (typeof analysisResult.basic_stats[field] !== 'number' || isNaN(analysisResult.basic_stats[field])) {
        errors.push(`Invalid basic_stats.${field}: must be a valid number`);
      }
    }
    
    // Validate reasonable ranges
    if (analysisResult.basic_stats.mean_sbp < 50 || analysisResult.basic_stats.mean_sbp > 300) {
      warnings.push(`Suspicious SBP value: ${analysisResult.basic_stats.mean_sbp}`);
    }
    
    if (analysisResult.basic_stats.mean_dbp < 30 || analysisResult.basic_stats.mean_dbp > 200) {
      warnings.push(`Suspicious DBP value: ${analysisResult.basic_stats.mean_dbp}`);
    }
  }
  
  // Validate total readings
  if (typeof analysisResult.total_readings !== 'number' || analysisResult.total_readings < 0) {
    errors.push('total_readings must be a non-negative number');
  }
  
  // Validate alerts array
  if (!Array.isArray(analysisResult.alerts)) {
    errors.push('alerts must be an array');
  }
  
  // Validate classification counts
  if (analysisResult.classification_counts) {
    const validClassifications = ['normal', 'elevated', 'stage1_hypertension', 'stage2_hypertension', 'hypertensive_crisis'];
    for (const [key, value] of Object.entries(analysisResult.classification_counts)) {
      if (!validClassifications.includes(key)) {
        warnings.push(`Unknown classification: ${key}`);
      }
      if (typeof value !== 'number' || value < 0) {
        errors.push(`Invalid classification count for ${key}: must be non-negative number`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    validatedData: analysisResult
  };
};

/**
 * Create fallback metrics for failed duration processing
 * @param {string} duration - Duration name
 * @param {string} error - Error message
 * @param {Object} dateRange - Date range for the duration
 * @returns {Object} Fallback metrics object
 */
export const createFallbackMetrics = (duration, error, dateRange) => {
  const config = {
    '1Month': { days: 30, label: '1 Month' },
    '3Months': { days: 90, label: '3 Months' },
    '6Months': { days: 180, label: '6 Months' },
    '1Year': { days: 365, label: '1 Year' },
    '2Years': { days: 730, label: '2 Years' },
    'All': { days: null, label: 'All Time' }
  };
  
  const durationConfig = config[duration] || { days: null, label: duration };
  
  return {
    duration: duration,
    days: durationConfig.days,
    totalReadings: 0,
    dateRange: dateRange,
    basicStats: {
      mean_sbp: null,
      mean_dbp: null,
      map: null,
      sbp_sd: null,
      dbp_sd: null
    },
    variabilityStats: {
      sbp_sd: null,
      dbp_sd: null,
      sbp_cv: null,
      dbp_cv: null
    },
    rangeStats: {
      sbp_range: null,
      dbp_range: null,
      sbp_min: null,
      sbp_max: null,
      dbp_min: null,
      dbp_max: null
    },
    pressureStats: {
      pulse_pressure: null,
      mean_arterial_pressure: null
    },
    circadianStats: {
      morning_sbp: null,
      evening_sbp: null,
      night_sbp: null,
      morning_dbp: null,
      evening_dbp: null,
      night_dbp: null
    },
    timeOfDay: {
      morning_sbp: null,
      evening_sbp: null,
      night_sbp: null,
      morning_dbp: null,
      evening_dbp: null,
      night_dbp: null
    },
    classificationCounts: {
      normal: 0,
      elevated: 0,
      stage1_hypertension: 0,
      stage2_hypertension: 0,
      hypertensive_crisis: 0
    },
    alerts: [`Processing failed: ${error}`],
    combinedStats: {
      overall_health_score: null,
      risk_level: 'unknown'
    },
    lastReading: null,
    computedAt: new Date(),
    isFallback: true,
    error: error
  };
};

/**
 * Validate stored duration metrics
 * @param {Object} metrics - Stored metrics to validate
 * @param {string} duration - Duration name
 * @returns {Object} Validation result
 */
export const validateStoredMetrics = (metrics, duration) => {
  const errors = [];
  const warnings = [];
  
  if (!metrics) {
    errors.push('Metrics object is null or undefined');
    return { isValid: false, errors, warnings };
  }
  
  // Check required fields
  const requiredFields = ['duration', 'totalReadings', 'dateRange', 'computedAt'];
  for (const field of requiredFields) {
    if (!metrics[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }
  
  // Validate duration matches
  if (metrics.duration !== duration) {
    errors.push(`Duration mismatch: expected ${duration}, got ${metrics.duration}`);
  }
  
  // Validate totalReadings
  if (typeof metrics.totalReadings !== 'number' || metrics.totalReadings < 0) {
    errors.push('totalReadings must be a non-negative number');
  }
  
  // Validate dateRange
  if (metrics.dateRange) {
    if (!metrics.dateRange.start || !metrics.dateRange.end) {
      errors.push('dateRange must have start and end properties');
    } else {
      const start = new Date(metrics.dateRange.start);
      const end = new Date(metrics.dateRange.end);
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        errors.push('dateRange start and end must be valid dates');
      } else if (start >= end) {
        errors.push('dateRange start must be before end');
      }
    }
  }
  
  // Validate computedAt
  if (metrics.computedAt) {
    const computedAt = new Date(metrics.computedAt);
    if (isNaN(computedAt.getTime())) {
      errors.push('computedAt must be a valid date');
    } else {
      const now = new Date();
      const age = now - computedAt;
      if (age > 7 * 24 * 60 * 60 * 1000) { // 7 days
        warnings.push('Metrics are older than 7 days');
      }
    }
  }
  
  // Check for fallback indicators
  if (metrics.isFallback) {
    warnings.push('Metrics are fallback data due to processing failure');
  }
  
  if (metrics.error) {
    warnings.push(`Metrics contain error: ${metrics.error}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    isStale: warnings.some(w => w.includes('older than 7 days')),
    isFallback: metrics.isFallback || false
  };
};

/**
 * Get data quality score for metrics
 * @param {Object} metrics - Metrics to score
 * @returns {Object} Quality score and details
 */
export const getDataQualityScore = (metrics) => {
  if (!metrics) {
    return { score: 0, details: ['No metrics provided'] };
  }
  
  let score = 100;
  const details = [];
  
  // Check if it's fallback data
  if (metrics.isFallback) {
    score -= 50;
    details.push('Fallback data (processing failed)');
  }
  
  // Check for errors
  if (metrics.error) {
    score -= 30;
    details.push('Contains processing errors');
  }
  
  // Check data completeness
  if (metrics.totalReadings === 0) {
    score -= 20;
    details.push('No readings available');
  } else if (metrics.totalReadings < 10) {
    score -= 10;
    details.push('Very few readings (< 10)');
  }
  
  // Check data freshness
  if (metrics.computedAt) {
    const age = new Date() - new Date(metrics.computedAt);
    const ageDays = age / (24 * 60 * 60 * 1000);
    
    if (ageDays > 7) {
      score -= 20;
      details.push(`Data is ${Math.round(ageDays)} days old`);
    } else if (ageDays > 1) {
      score -= 5;
      details.push(`Data is ${Math.round(ageDays)} days old`);
    }
  }
  
  // Check for null values in critical fields
  if (metrics.basicStats) {
    const criticalFields = ['mean_sbp', 'mean_dbp'];
    const nullFields = criticalFields.filter(field => 
      metrics.basicStats[field] === null || metrics.basicStats[field] === undefined
    );
    
    if (nullFields.length > 0) {
      score -= 15;
      details.push(`Missing critical data: ${nullFields.join(', ')}`);
    }
  }
  
  // Determine quality level
  let qualityLevel = 'excellent';
  if (score < 80) qualityLevel = 'good';
  if (score < 60) qualityLevel = 'fair';
  if (score < 40) qualityLevel = 'poor';
  if (score < 20) qualityLevel = 'critical';
  
  return {
    score: Math.max(0, score),
    qualityLevel,
    details,
    isReliable: score >= 60,
    needsRefresh: score < 40
  };
};

/**
 * Validate and repair stored metrics if needed
 * @param {string} patientId - Patient ID
 * @param {string} duration - Duration name
 * @returns {Object} Repair result
 */
export const validateAndRepairMetrics = async (patientId, duration) => {
  try {
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    // Get current metrics
    const metricsDoc = await collection.findOne({ patientId });
    if (!metricsDoc) {
      return { repaired: false, reason: 'No metrics found for patient' };
    }
    
    const metrics = metricsDoc[`metrics${duration}`];
    if (!metrics) {
      return { repaired: false, reason: `No ${duration} metrics found` };
    }
    
    // Validate metrics
    const validation = validateStoredMetrics(metrics, duration);
    
    if (validation.isValid) {
      return { repaired: false, reason: 'Metrics are valid' };
    }
    
    // Attempt repair
    console.log(`🔧 Repairing ${duration} metrics for patient ${patientId}`);
    
    // Create fallback metrics
    const fallbackMetrics = createFallbackMetrics(
      duration,
      `Validation failed: ${validation.errors.join(', ')}`,
      metrics.dateRange || { start: null, end: null }
    );
    
    // Update with repaired metrics
    await collection.updateOne(
      { patientId },
      { 
        $set: { 
          [`metrics${duration}`]: fallbackMetrics,
          lastUpdated: new Date(),
          updatedAt: new Date()
        }
      }
    );
    
    console.log(`✅ Repaired ${duration} metrics for patient ${patientId}`);
    
    return { 
      repaired: true, 
      reason: 'Metrics repaired with fallback data',
      repairedMetrics: fallbackMetrics
    };
    
  } catch (error) {
    console.error(`❌ Error repairing metrics for ${patientId}/${duration}:`, error);
    return { 
      repaired: false, 
      reason: `Repair failed: ${error.message}`,
      error: error.message
    };
  }
};

/**
 * Get comprehensive data health report for a patient
 * @param {string} patientId - Patient ID
 * @returns {Object} Health report
 */
export const getDataHealthReport = async (patientId) => {
  try {
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    const metricsDoc = await collection.findOne({ patientId });
    if (!metricsDoc) {
      return { 
        patientId, 
        status: 'no_data', 
        message: 'No duration metrics found for patient',
        durations: {}
      };
    }
    
    const durations = ['1Month', '3Months', '6Months', '1Year', '2Years', 'All'];
    const healthReport = {
      patientId,
      status: 'healthy',
      lastUpdated: metricsDoc.lastUpdated,
      durations: {},
      overallScore: 0,
      needsAttention: []
    };
    
    let totalScore = 0;
    let validDurations = 0;
    
    for (const duration of durations) {
      const metrics = metricsDoc[`metrics${duration}`];
      if (!metrics) {
        healthReport.durations[duration] = {
          status: 'missing',
          score: 0,
          message: 'No metrics available'
        };
        healthReport.needsAttention.push(`${duration}: Missing metrics`);
        continue;
      }
      
      const validation = validateStoredMetrics(metrics, duration);
      const quality = getDataQualityScore(metrics);
      
      healthReport.durations[duration] = {
        status: validation.isValid ? 'valid' : 'invalid',
        score: quality.score,
        qualityLevel: quality.qualityLevel,
        isReliable: quality.isReliable,
        needsRefresh: quality.needsRefresh,
        isFallback: metrics.isFallback || false,
        totalReadings: metrics.totalReadings || 0,
        lastComputed: metrics.computedAt,
        issues: [...validation.errors, ...validation.warnings]
      };
      
      if (quality.needsRefresh) {
        healthReport.needsAttention.push(`${duration}: Needs refresh (score: ${quality.score})`);
      }
      
      if (metrics.isFallback) {
        healthReport.needsAttention.push(`${duration}: Using fallback data`);
      }
      
      totalScore += quality.score;
      validDurations++;
    }
    
    // Calculate overall score
    healthReport.overallScore = validDurations > 0 ? Math.round(totalScore / validDurations) : 0;
    
    // Determine overall status
    if (healthReport.overallScore < 40) {
      healthReport.status = 'critical';
    } else if (healthReport.overallScore < 60) {
      healthReport.status = 'poor';
    } else if (healthReport.overallScore < 80) {
      healthReport.status = 'fair';
    } else {
      healthReport.status = 'healthy';
    }
    
    return healthReport;
    
  } catch (error) {
    console.error(`❌ Error generating health report for ${patientId}:`, error);
    return {
      patientId,
      status: 'error',
      message: `Failed to generate health report: ${error.message}`,
      durations: {}
    };
  }
};






