# R BP Analysis Pipeline

This pipeline processes blood pressure data using R libraries (`bp` and `jsonlite`) in a MERN stack environment.

## Overview

The pipeline follows an on-write approach:
1. **Data Collection**: Fetches BP readings from `ad_bpms` collection
2. **Storage**: Saves raw readings to `R_BP` database
3. **Analysis**: Uses R scripts to analyze BP data
4. **Results**: Stores processed metrics and series

## Environment Variables

Add these to your `.env` file:

```bash
# R BP Pipeline Configuration (NEW - add these)
MONGODB_R_BP_URI=mongodb://localhost:27017/R_BP
R_SCRIPT_PATH=/usr/bin/Rscript
CURRENT_ANALYSIS_VERSION=v1.0.0
FEATURE_BP_ON_WRITE=true
SAMPLE_PATIENT_NAME=Sample Patient (for local examples)
SAMPLE_PATIENT_IMEI=000000000000000

# Note: Uses existing mongoUri for Cardiowell database access
```

## Database Schema

### Collections in R_BP database:

1. **bp_readings** - Raw BP readings
   - `patientId`, `readingAtUTC`, `sbp`, `dbp`, `pulse`
   - `deviceType`, `deviceId`, `source`, `contentHash`

2. **bp_processed** - Processed analysis results
   - `patientId`, `day`, `analysisVersion`, `computedAt`
   - `statistics`, `classificationCounts`, `series`

3. **bp_summary** - Patient summaries
   - `patientId`, `lastUpdated`, `statistics`, `latestReading`

## Usage

### Initialize Database
```bash
npm run bp:init
```

### Example local testing (optional)
```bash
# List available days
# List days / process data using your own IMEI with scripts as applicable
# (Customize commands per your environment)
```

### General Commands
```bash
# List available days for device
npm run bp:list-days -- --imei <IMEI>

# Process specific day
npm run bp:process-day -- --imei <IMEI> --day 2025-09-10

# Process all days
npm run bp:process-all -- --imei <IMEI>

# Get processed results
npm run bp:get-results -- --imei <IMEI>
```

## R Script

The R script (`scripts/r/bp_analyze.R`) performs:
- Data validation and cleaning
- BP classification using `bp` package
- Statistical analysis (mean, variability, MAP)
- Time series processing

## Data Flow

1. **Fetch**: Read from `ad_bpms` collection using IMEI
2. **Normalize**: Convert to standard format with validation
3. **Hash**: Create content/source hashes for deduplication
4. **Store**: Save raw readings to `bp_readings`
5. **Analyze**: Call R script with JSON data
6. **Process**: Store results in `bp_processed` and `bp_summary`

## Features

- ✅ On-write processing (real-time after device scan)
- ✅ R integration with `bp` and `jsonlite` packages
- ✅ Data deduplication and validation
- ✅ Daily windowing for analysis
- ✅ Versioning support for algorithm updates
- ✅ Terminal-based testing and execution
- ✅ MongoDB indexes for performance

## Requirements

- Node.js with MongoDB driver
- R with `bp` and `jsonlite` packages
- MongoDB running locally
- Test data in `ad_bpms` collection

## Testing

Example usage with your own device IMEI:

```bash
# Initialize database
npm run bp:init

# Test with Test Ram data
# See commands above for listing and processing
```
