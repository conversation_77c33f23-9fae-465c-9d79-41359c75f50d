#!/usr/bin/env node

import { createRBPIndexes, getRBPDB, createDurationMetricsDocument } from './mongoConnection.mjs';
import { closeConnections } from './mongoConnection.mjs';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Initialize R_BP database with enhanced schema for 6 fixed durations
 */
const initializeDatabaseSchema = async () => {
  try {
    console.log('🚀 Initializing R_BP database schema for Phase 1...');
    
    // Create all indexes including bp_duration_metrics
    await createRBPIndexes();
    
    // Test the new collection by creating a sample document
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    // Create a test document to verify schema
    const testPatientId = 'test_patient_schema_validation';
    const testDoc = createDurationMetricsDocument(testPatientId);
    
    // Insert test document
    await collection.insertOne(testDoc);
    console.log('✅ Test document created successfully');
    
    // Verify document structure
    const retrievedDoc = await collection.findOne({ patientId: testPatientId });
    if (retrievedDoc) {
      console.log('✅ Document retrieval successful');
      console.log('📊 Available durations:', Object.keys(retrievedDoc).filter(key => key.startsWith('metrics')));
      console.log('📈 Sample metrics structure:', {
        duration: retrievedDoc.metrics1Month.duration,
        days: retrievedDoc.metrics1Month.days,
        totalReadings: retrievedDoc.metrics1Month.totalReadings,
        hasBasicStats: !!retrievedDoc.metrics1Month.basicStats,
        hasVariabilityStats: !!retrievedDoc.metrics1Month.variabilityStats,
        hasRangeStats: !!retrievedDoc.metrics1Month.rangeStats,
        hasPressureStats: !!retrievedDoc.metrics1Month.pressureStats,
        hasCircadianStats: !!retrievedDoc.metrics1Month.circadianStats,
        hasTimeOfDay: !!retrievedDoc.metrics1Month.timeOfDay,
        hasClassificationCounts: !!retrievedDoc.metrics1Month.classificationCounts,
        hasAlerts: !!retrievedDoc.metrics1Month.alerts,
        hasCombinedStats: !!retrievedDoc.metrics1Month.combinedStats
      });
    }
    
    // Clean up test document
    await collection.deleteOne({ patientId: testPatientId });
    console.log('✅ Test document cleaned up');
    
    console.log('🎉 Database schema initialization completed successfully!');
    console.log('📋 Schema includes:');
    console.log('   - bp_readings (existing)');
    console.log('   - bp_processed (existing)');
    console.log('   - bp_summary (existing)');
    console.log('   - bp_duration_metrics (NEW - 6 fixed durations)');
    console.log('   - All necessary indexes created');
    
  } catch (error) {
    console.error('❌ Database schema initialization failed:', error);
    throw error;
  } finally {
    await closeConnections();
  }
};

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeDatabaseSchema()
    .then(() => {
      console.log('✅ Database schema initialization completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database schema initialization failed:', error);
      process.exit(1);
    });
}

export { initializeDatabaseSchema };
