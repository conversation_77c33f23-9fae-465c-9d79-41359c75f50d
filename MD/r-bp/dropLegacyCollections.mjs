import { getRBPDB, closeConnections } from './mongoConnection.mjs'

const collectionsToDrop = ['bp_readings', 'bp_processed', 'bp_summary']

async function run() {
  try {
    const db = await getRBPDB()

    for (const name of collectionsToDrop) {
      const exists = await db.listCollections({ name }).hasNext()
      if (exists) {
        console.log(`Dropping collection: ${name}`)
        await db.collection(name).drop()
        console.log(`✅ Dropped: ${name}`)
      } else {
        console.log(`ℹ️  Collection not found (skip): ${name}`)
      }
    }

    console.log('✅ Completed dropping legacy collections')
  } catch (err) {
    console.error('❌ Error dropping legacy collections:', err)
    process.exitCode = 1
  } finally {
    await closeConnections()
  }
}

run()


