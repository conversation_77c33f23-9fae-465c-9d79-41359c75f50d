import pkg from 'mongodb';
const { MongoClient } = pkg;
import dotenv from 'dotenv';

dotenv.config();

// MongoDB connections
let cardiowellClient = null;

// Connect to Cardiowell database (for reading ad_bpms data)
export const connectToCardiowell = async () => {
  if (!cardiowellClient) {
    const uri = process.env.mongoUri || 'mongodb://localhost:27017/cardiowell_local';
    cardiowellClient = new MongoClient(uri);
    await cardiowellClient.connect();
    console.log('Connected to Cardiowell database');
  }
  return cardiowellClient;
};

// Connect to R_BP database (for storing processed data)
// DEPRECATED: R_BP now aliases to the main Cardiowell database
export const connectToRBP = async () => {
  const client = await connectToCardiowell();
  console.log('Using Cardiowell database for former R_BP collections');
  return client;
};

// Get Cardiowell database
export const getCardiowellDB = async () => {
  const client = await connectToCardiowell();
  return client.db();
};

// Get R_BP database
export const getRBPDB = async () => {
  // Alias to Cardiowell DB so there is a single database
  const client = await connectToCardiowell();
  return client.db();
};

// Close all connections
export const closeConnections = async () => {
  if (cardiowellClient) {
    await cardiowellClient.close();
    cardiowellClient = null;
  }
  console.log('All database connections closed');
};

// Create indexes for R_BP collections
export const createRBPIndexes = async () => {
  const db = await getRBPDB();
  
  // (Removed) Legacy bp_readings/bp_processed/bp_summary index creation
  
  // bp_duration_metrics indexes (Phase 1 + Phase 5 enhancements)
  await db.collection('bp_duration_metrics').createIndex({ patientId: 1 }, { unique: true });
  await db.collection('bp_duration_metrics').createIndex({ lastUpdated: -1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics1Month.totalReadings': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics3Months.totalReadings': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics6Months.totalReadings': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics1Year.totalReadings': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics2Years.totalReadings': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metricsAll.totalReadings': 1 });
  
  // Phase 5: Enhanced indexes for performance and validation
  await db.collection('bp_duration_metrics').createIndex({ 'metrics1Month.computedAt': -1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics3Months.computedAt': -1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics6Months.computedAt': -1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics1Year.computedAt': -1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics2Years.computedAt': -1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metricsAll.computedAt': -1 });
  
  // Phase 5: Indexes for quality and validation tracking
  await db.collection('bp_duration_metrics').createIndex({ 'metrics1Month.isFallback': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics3Months.isFallback': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics6Months.isFallback': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics1Year.isFallback': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metrics2Years.isFallback': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'metricsAll.isFallback': 1 });
  
  // Phase 5: Indexes for processing statistics
  await db.collection('bp_duration_metrics').createIndex({ 'processingStats.successful': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'processingStats.failed': 1 });
  await db.collection('bp_duration_metrics').createIndex({ 'processingStats.fallback': 1 });
  
  // Phase 5: Event error logging collection
  await db.collection('bp_event_errors').createIndex({ imei: 1 });
  await db.collection('bp_event_errors').createIndex({ loggedAt: -1 });
  await db.collection('bp_event_errors').createIndex({ resolved: 1 });
  await db.collection('bp_event_errors').createIndex({ deviceType: 1 });
  
  console.log('R_BP database indexes created successfully (including bp_duration_metrics)');
};

// Create bp_duration_metrics document structure
export const createDurationMetricsDocument = (patientId) => {
  return {
    patientId,
    lastUpdated: new Date(),
    
    // 6 Fixed Durations with Pre-Computed Metrics
    metrics1Month: {
      duration: '1Month',
      days: 30,
      totalReadings: 0,
      dateRange: { start: null, end: null },
      basicStats: {},
      variabilityStats: {},
      rangeStats: {},
      pressureStats: {},
      circadianStats: {},
      timeOfDay: {},
      classificationCounts: {},
      alerts: [],
      combinedStats: {},
      lastReading: null,
      computedAt: null
    },
    
    metrics3Months: {
      duration: '3Months',
      days: 90,
      totalReadings: 0,
      dateRange: { start: null, end: null },
      basicStats: {},
      variabilityStats: {},
      rangeStats: {},
      pressureStats: {},
      circadianStats: {},
      timeOfDay: {},
      classificationCounts: {},
      alerts: [],
      combinedStats: {},
      lastReading: null,
      computedAt: null
    },
    
    metrics6Months: {
      duration: '6Months',
      days: 180,
      totalReadings: 0,
      dateRange: { start: null, end: null },
      basicStats: {},
      variabilityStats: {},
      rangeStats: {},
      pressureStats: {},
      circadianStats: {},
      timeOfDay: {},
      classificationCounts: {},
      alerts: [],
      combinedStats: {},
      lastReading: null,
      computedAt: null
    },
    
    metrics1Year: {
      duration: '1Year',
      days: 365,
      totalReadings: 0,
      dateRange: { start: null, end: null },
      basicStats: {},
      variabilityStats: {},
      rangeStats: {},
      pressureStats: {},
      circadianStats: {},
      timeOfDay: {},
      classificationCounts: {},
      alerts: [],
      combinedStats: {},
      lastReading: null,
      computedAt: null
    },
    
    metrics2Years: {
      duration: '2Years',
      days: 730,
      totalReadings: 0,
      dateRange: { start: null, end: null },
      basicStats: {},
      variabilityStats: {},
      rangeStats: {},
      pressureStats: {},
      circadianStats: {},
      timeOfDay: {},
      classificationCounts: {},
      alerts: [],
      combinedStats: {},
      lastReading: null,
      computedAt: null
    },
    
    metricsAll: {
      duration: 'All',
      days: null,
      totalReadings: 0,
      dateRange: { start: null, end: null },
      basicStats: {},
      variabilityStats: {},
      rangeStats: {},
      pressureStats: {},
      circadianStats: {},
      timeOfDay: {},
      classificationCounts: {},
      alerts: [],
      combinedStats: {},
      lastReading: null,
      computedAt: null
    },
    
    createdAt: new Date(),
    updatedAt: new Date()
  };
};

// Get duration metrics for a patient
export const getDurationMetrics = async (patientId) => {
  const db = await getRBPDB();
  const collection = db.collection('bp_duration_metrics');
  
  const metrics = await collection.findOne({ patientId });
  
  if (!metrics) {
    // Create new document if doesn't exist
    const newDoc = createDurationMetricsDocument(patientId);
    await collection.insertOne(newDoc);
    return newDoc;
  }
  
  return metrics;
};

// Update duration metrics for a patient
export const updateDurationMetrics = async (patientId, durationName, metrics) => {
  const db = await getRBPDB();
  const collection = db.collection('bp_duration_metrics');
  
  const updateDoc = {
    [`metrics${durationName}`]: {
      ...metrics,
      computedAt: new Date()
    },
    lastUpdated: new Date(),
    updatedAt: new Date()
  };
  
  await collection.updateOne(
    { patientId },
    { $set: updateDoc },
    { upsert: true }
  );
  
  console.log(`✅ Updated ${durationName} metrics for patient ${patientId}`);
};

// Get all duration metrics for a patient
export const getAllDurationMetrics = async (patientId) => {
  const db = await getRBPDB();
  const collection = db.collection('bp_duration_metrics');
  
  const metrics = await collection.findOne({ patientId });
  
  if (!metrics) {
    console.log(`No duration metrics found for patient ${patientId}`);
    return null;
  }
  // Ensure all 6 duration blocks exist; if any is missing, provide an empty default
  const defaults = createDurationMetricsDocument(patientId);

  return {
    patientId: metrics.patientId,
    lastUpdated: metrics.lastUpdated,
    durations: {
      '1Month': metrics.metrics1Month || defaults.metrics1Month,
      '3Months': metrics.metrics3Months || defaults.metrics3Months,
      '6Months': metrics.metrics6Months || defaults.metrics6Months,
      '1Year': metrics.metrics1Year || defaults.metrics1Year,
      '2Years': metrics.metrics2Years || defaults.metrics2Years,
      'All': metrics.metricsAll || defaults.metricsAll
    }
  };
};
