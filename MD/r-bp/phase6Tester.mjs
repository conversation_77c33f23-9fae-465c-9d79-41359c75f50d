import dotenv from 'dotenv';
import analyticsClient from '../../utils/analyticsClient.mjs';
import { emitNewBPReading } from './bpEventSystem.mjs';

dotenv.config();

const run = async () => {
  console.log('Checking analytics health...');
  console.log(await analyticsClient.health());

  const testImei = process.argv[2] || '352847091234567';
  console.log(`Emitting test newBPReading for IMEI=${testImei}`);
  emitNewBPReading(testImei, 'test', 'phase6Tester', new Date().toISOString());
};

run().catch((e) => {
  console.error(e);
  process.exit(1);
});






