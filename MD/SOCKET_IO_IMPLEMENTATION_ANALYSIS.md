## Executive Summary

CardioWell uses Socket.IO (server v2.5.x, client v2.3.x) to deliver real-time patient data to the provider and patient dashboards, and to push updates when device data (e.g., Withings) is ingested. The backend initializes a single Socket.IO server in cardiowell-backend/app.mjs and exposes it via app.set("socketio"). The frontend connects from multiple React components using socket.io-client, listening for data updates and triggering server fetches via socket events.

Primary events:
- patientData (bidirectional): stream of clinic patient overviews for a provider
- patientDataUpdate (bidirectional): one-time refresh of clinic patient overviews
- patientDashboardData (bidirectional): fetch a single patient's full dataset
- iFrameData (bidirectional): stream clinic patients list for the IFrame view
- withingsDataUpdate (server→client broadcast): notify UIs to refresh after Withings webhook events

Security is light-weight: select events validate provider/clinic IDs against the DB; there is no dedicated socket authentication middleware. Rooms/namespaces are not used; global broadcasts rely on client-side filtering.

---

## 1) Socket Usage Discovery

Backend (cardiowell-backend/):
- Socket.IO server initialization and event handlers
  - cardiowell-backend/app.mjs: lines 238–299 (connection handler and events); lines 238–241 (server init)
- Emitting from REST-handling code via app.get('socketio')
  - cardiowell-backend/withings/controller/update.mjs: lines 17, 39 (retrieve ioServer and pass to service)
  - cardiowell-backend/withings/service/onNotification.mjs: lines 61–64 (emit 'withingsDataUpdate')
- Dependencies
  - cardiowell-backend/package.json: dependency "socket.io": "^2.3.0"
  - cardiowell-backend/yarn.lock: resolves socket.io@2.5.1 and socket.io-client@2.5.0 (transitive)

Frontend (patientCareReact-master/):
- Client initialization and usage
  - patientCareReact-master/src/components/DashboardPage.js: lines 45–46, 224–246, 247–267, 269–273
  - patientCareReact-master/src/components/PatientDashboard.js: lines 37–38, 155–167
  - patientCareReact-master/src/components/IFramePage.js: lines 60, 975–981
  - patientCareReact-master/src/common/common.js: WEBSOCKET_URL wrapper
  - patientCareReact-master/local-proxy.js: proxies /socket.io for local dev (ws: true)
- Dependencies
  - patientCareReact-master/package.json: dependency "socket.io-client": "^2.3.0"

Code excerpts:
<augment_code_snippet path="cardiowell-backend/app.mjs" mode="EXCERPT">
````javascript
const httpServer = http.Server(app)
const ioServer = io(httpServer)
app.set("socketio", ioServer)
ioServer.on("connection", function (socket) {
  socket.on("patientData", (emittedData) => { /* ... */ })
  socket.on("patientDashboardData", async (emittedData) => { /* ... */ })
})
````
</augment_code_snippet>

<augment_code_snippet path="cardiowell-backend/withings/service/onNotification.mjs" mode="EXCERPT">
````javascript
getPatientDataById(patientId).then(patient => {
  const clinic = patient?.patientObj?.clinic
  clinic && socket.emit("withingsDataUpdate", { patientId, clinic })
})
````
</augment_code_snippet>

<augment_code_snippet path="patientCareReact-master/src/components/DashboardPage.js" mode="EXCERPT">
````javascript
let socket = openSocket(WEBSOCKET_URL)
// ...
socket.emit('patientData', { clinic, providerID })
socket.on('patientData', (data) => { /* setPatients */ })
socket.on('withingsDataUpdate', (data) => { /* emit refresh */ })
````
</augment_code_snippet>

---

## 2) Functional Analysis (Events)

Event: patientData
- Direction: client→server (request) and server→client (streamed responses)
- Purpose: Provide periodic (10s) clinic patient overviews for a provider dashboard
- Trigger/conditions (server):
  - On socket.on('patientData', { clinic, providerID })
  - Validates Provider by _id; if found, starts setInterval every 10s
- Handler location: cardiowell-backend/app.mjs lines 246–257
- Server response: socket.emit('patientData', patientArray) every 10s; else 'Error: Not Authenticated'
- Payloads:
  - Request: { clinic: string, providerID: string }
  - Response success: Array<PatientOverview>
  - Response error: string "Error: Not Authenticated"

Event: patientDataUpdate
- Direction: client→server; server→client (one-time)
- Purpose: On-demand single refresh of the clinic patient overviews (used after withingsDataUpdate)
- Trigger/conditions: socket.on('patientDataUpdate', { clinic, providerID }); validates Provider
- Handler location: app.mjs lines 263–275
- Response: socket.emit('patientDataUpdate', patientArray) or error string
- Payloads: same as patientData

Event: patientDashboardData
- Direction: client→server; server→client (one-time)
- Purpose: Retrieve a single patient’s full data object for the dashboards
- Trigger/conditions: socket.on('patientDashboardData', { id })
- Handler location: app.mjs lines 278–285
- Response: socket.emit('patientDashboardData', patient.patientObj)
- Payloads:
  - Request: { id: PatientId }
  - Response: PatientFull
- Note: No explicit provider/clinic validation for this event

Event: iFrameData
- Direction: client→server; server→client (streamed)
- Purpose: Provide periodic (10s) clinic-wide patient list for the IFrame view
- Trigger/conditions: socket.on('iFrameData', { clinicID, clinic }); validates Clinic by _id
- Handler location: app.mjs lines 287–297
- Response: socket.emit('iFrameData', patientArray) every 10s; or error string

Event: withingsDataUpdate
- Direction: server→client broadcast (global ioServer.emit)
- Purpose: Notify clients that new Withings data arrived, prompting UI refresh
- Trigger/conditions: Called from Withings webhook flow after saving BP data and resolving patient/clinic
- Emit locations:
  - withings/controller/update.mjs lines 17–27 and 39–49: acquires socket via request.app.get('socketio') and passes it down
  - withings/service/onNotification.mjs lines 61–64: emits { patientId, clinic } to all clients
- Payload: { patientId: string, clinic: string }
- Typical client handling:
  - Provider dashboard: if clinic matches, emit 'patientDataUpdate' to refresh list; also refresh selected patient's data
  - Patient dashboard: if patientId matches session patientID, emit 'patientDashboardData'

Data payload structures (high-level):
- PatientOverview: projected subset from lookupPatientOverview(); includes recent readings, name, timeZone, etc.
- PatientFull: aggregate of all readings and threshold info via getPatientDataById(); includes timeZone and various device data arrays; see users/service/patientData.mjs lines 66–90, 132–147

---

## 3) Architecture Integration

- REST endpoints and routes
  - Frontend fetches initial data over REST and then maintains live updates over sockets:
    - /routes/users/getClinicPatientOverviews (DashboardPage.js lines 209–219)
    - /routes/users/getPatientData (PatientDashboard.js lines 139–150)
  - Withings webhook endpoints (/routes/withings/update, /routes/withings/dev/update) trigger server-side socket broadcasts to notify UIs
- Database operations and models
  - Patient aggregation pipelines (users/service/patientData.mjs) build overviews and full data using MongoDB aggregations and device collections; these are called by socket handlers
- Frontend components and state management
  - Provider Dashboard (DashboardPage.js) sets patients list and selected patient state in response to socket updates
  - Patient Dashboard (PatientDashboard.js) updates detailed view on patientDashboardData; both listen to withingsDataUpdate
  - IFramePage.js maintains a simple patients list via iFrameData
- Authentication and authorization
  - Express uses passport/session for HTTP routes, but Socket.IO connections themselves do not use auth middleware
  - Event-level checks:
    - patientData and patientDataUpdate: validate Provider by _id
    - iFrameData: validate Clinic by _id
    - patientDashboardData: no validation
- Middleware and error handling
  - No Socket.IO-specific middleware; Express-level Sentry/errorhandler used for HTTP, not sockets

---

## 4) Connection Lifecycle

- Establishment
  - Client connects via openSocket(WEBSOCKET_URL); local dev proxy forwards /socket.io to API
- Authentication/authorization
  - No handshake auth; event payloads include providerID/clinicID for selective validation
- Persistence and reconnection
  - Relies on socket.io-client defaults (auto-reconnect); no app-level custom logic
- Disconnection and cleanup
  - Server logs 'User Disconnected' on disconnect; intervals created in patientData/iFrameData are not explicitly cleared on disconnect
- Rooms/namespaces
  - None used; withingsDataUpdate is a global broadcast; clients filter payloads (clinic/patientId)

---

## 5) Error Handling and Reliability

- Error patterns
  - Event handlers emit literal string "Error: Not Authenticated" on validation failure; no standardized error event or error codes
  - Exceptions in patientDashboardData are caught and logged to console; no error emit
- Timeout/retry
  - Not configured beyond socket.io defaults; setInterval used for periodic polling on the server per connection
- Fallbacks
  - Frontends fetch initial data via REST; socket failures would leave last known state until refresh
- Logging/monitoring
  - Minimal console.log on disconnect; Express has Sentry integration, but no Socket.IO instrumentation

---

## Code References (paths and line numbers)

- Server setup and handlers: cardiowell-backend/app.mjs lines 238–299
- Withings broadcast: cardiowell-backend/withings/service/onNotification.mjs lines 61–64
- Withings controller acquiring socket: cardiowell-backend/withings/controller/update.mjs lines 17, 39
- Patient data services: cardiowell-backend/users/service/patientData.mjs lines 9–25, 32–40, 66–90, 132–147
- Frontend sockets:
  - DashboardPage.js: lines 45–46, 224–246, 247–267, 269–273
  - PatientDashboard.js: lines 37–38, 155–167
  - IFramePage.js: lines 60, 975–981
  - local-proxy.js: lines 15–21

---

## Data Flow Diagram (Mermaid)

```mermaid
sequenceDiagram
  participant Device as Withings
  participant API as Backend (Express)
  participant SIO as Socket.IO Server
  participant Prov as Provider UI
  participant Pat as Patient UI

  Device->>API: POST /routes/withings/update
  API->>SIO: socket.emit('withingsDataUpdate', {patientId, clinic})
  SIO-->>Prov: withingsDataUpdate
  SIO-->>Pat: withingsDataUpdate
  Prov->>SIO: emit('patientDataUpdate', {clinic, providerID})
  SIO-->>Prov: patientDataUpdate [array]
  Prov->>SIO: emit('patientDashboardData', {id})
  SIO-->>Prov: patientDashboardData [object]
```

---

## Recommendations and Best Practices

1) Authentication and Authorization
- Add Socket.IO auth middleware (e.g., JWT in query/header or session integration) to authenticate on connect
- Validate patientDashboardData requests against provider/patient relationship and roles

2) Scoping and Efficiency
- Use rooms (e.g., room: clinic:<id>, patient:<id>, provider:<id>)
  - Emit withingsDataUpdate only to relevant rooms: io.to(`clinic:${clinic}`).emit(...)
- Avoid global broadcasts with client-side filtering

3) Interval Management and Resource Usage
- Replace per-socket setInterval polling with:
  - Server-side scheduled updates pushing to rooms, or
  - Client-initiated polling with clearInterval on disconnect
- Clear intervals on 'disconnect' to prevent leaks

4) API Design and Error Handling
- Standardize error events (e.g., 'error' with { code, message }) rather than magic strings
- Use acknowledgements (callbacks) for critical emits to confirm delivery

5) Observability
- Add Sentry (or similar) instrumentation for Socket.IO events and errors
- Add structured logs around connections, joins, leaves, and emits

6) Versioning and Maintenance
- Consider upgrading to Socket.IO v4 on both client and server for security and features
- Centralize event names as constants shared across client/server to avoid typos

7) Security
- Rate-limit event calls (e.g., patientDashboardData) to mitigate abuse
- Validate and sanitize all incoming payloads with schemas (e.g., zod) before DB calls

---

## Appendix: Minimal Code Excerpts

Server: withingsDataUpdate emit
<augment_code_snippet path="cardiowell-backend/withings/service/onNotification.mjs" mode="EXCERPT">
````javascript
const patientId = userData.patientId
getPatientDataById(patientId).then(patient => {
  const clinic = patient?.patientObj?.clinic
  clinic && socket.emit('withingsDataUpdate', { patientId, clinic })
})
````
</augment_code_snippet>

Client: react to Withings then refresh
<augment_code_snippet path="patientCareReact-master/src/components/DashboardPage.js" mode="EXCERPT">
````javascript
socket.on('withingsDataUpdate', (data) => {
  if (data.clinic && data.clinic === clinic && data.patientId) {
    socket.emit('patientDataUpdate', emittedData)
  }
})
````
</augment_code_snippet>

