# Placeholder BP analytics module

# Local null-coalescing helper to avoid dependency on plumber.R environment
coalesce_null <- function(x, y) {
  if (is.null(x) || identical(x, "")) y else x
}

#* Compute duration metrics for a patient
#* @post /v1/bp/compute-durations
#* @serializer json
function(req, res) {
  body <- tryCatch(jsonlite::fromJSON(req$postBody), error = function(e) NULL)
  if (is.null(body) || is.null(body$patientId)) {
    res$status <- 400
    return(list(error = "patientId is required"))
  }
  windows <- body$windows
  if (is.null(windows)) {
    windows <- c("1Month", "3Months", "6Months", "1Year", "2Years", "All")
  }
  # Placeholder deterministic response to enable integration
  now <- as.character(Sys.time())
  mk <- function(name, days) {
    list(
      duration = name,
      days = ifelse(is.null(days), NULL, days),
      totalReadings = 0,
      dateRange = list(start = NULL, end = NULL),
      basicStats = list(),
      variabilityStats = list(),
      rangeStats = list(),
      pressureStats = list(),
      circadianStats = list(),
      timeOfDay = list(),
      classificationCounts = list(),
      alerts = list(),
      combinedStats = list(),
      lastReading = NULL,
      computedAt = now,
      isFallback = TRUE
    )
  }
  out <- list()
  for (w in windows) {
    if (w == "1Month") {
      out[[w]] <- mk("1Month", 30)
    } else if (w == "3Months") {
      out[[w]] <- mk("3Months", 90)
    } else if (w == "6Months") {
      out[[w]] <- mk("6Months", 180)
    } else if (w == "1Year") {
      out[[w]] <- mk("1Year", 365)
    } else if (w == "2Years") {
      out[[w]] <- mk("2Years", 730)
    } else if (w == "All") {
      out[[w]] <- mk("All", NULL)
    }
  }
  list(patientId = body$patientId, windows = windows, metrics = out)
}

# ---- New R endpoints expected by Node ----

compute_basic_stats <- function(readings_df) {
  sbp <- suppressWarnings(as.numeric(readings_df$sbp))
  dbp <- suppressWarnings(as.numeric(readings_df$dbp))
  pulse <- suppressWarnings(as.numeric(readings_df$pulse))
  mapv <- ifelse(!is.na(sbp) & !is.na(dbp), dbp + (sbp - dbp) / 3, NA)
  # Pulse pressure per reading
  pp <- ifelse(!is.na(sbp) & !is.na(dbp), sbp - dbp, NA)
  sbp_sd <- ifelse(sum(!is.na(sbp)) < 2, NA, stats::sd(sbp, na.rm = TRUE))
  dbp_sd <- ifelse(sum(!is.na(dbp)) < 2, NA, stats::sd(dbp, na.rm = TRUE))
  pulse_sd <- ifelse(sum(!is.na(pulse)) < 2, NA, stats::sd(pulse, na.rm = TRUE))
  pp_sd <- ifelse(sum(!is.na(pp)) < 2, NA, stats::sd(pp, na.rm = TRUE))
  msbp <- ifelse(all(is.na(sbp)), NA, mean(sbp, na.rm = TRUE))
  mdbp <- ifelse(all(is.na(dbp)), NA, mean(dbp, na.rm = TRUE))
  mpulse <- ifelse(all(is.na(pulse)), NA, mean(pulse, na.rm = TRUE))
  mmap <- ifelse(all(is.na(mapv)), NA, mean(mapv, na.rm = TRUE))
  list(
    basic_stats = list(
      mean_sbp = msbp,
      mean_dbp = mdbp,
      mean_pulse = mpulse,
      map = mmap
    ),
    variability_stats = list(
      sbp_sd = sbp_sd,
      dbp_sd = dbp_sd,
      pulse_sd = pulse_sd,
      cv_sbp = ifelse(
        is.na(msbp) || msbp == 0 || is.na(sbp_sd),
        NA,
        round(100 * sbp_sd / msbp, 2)
      ),
      cv_dbp = ifelse(
        is.na(mdbp) || mdbp == 0 || is.na(dbp_sd),
        NA,
        round(100 * dbp_sd / mdbp, 2)
      ),
      cv_pulse = ifelse(
        is.na(mpulse) || mpulse == 0 || is.na(pulse_sd),
        NA,
        round(100 * pulse_sd / mpulse, 2)
      ),
      ppv_sd = pp_sd
    ),
    range_stats = list(
      sbp_min = ifelse(all(is.na(sbp)), NA, min(sbp, na.rm = TRUE)),
      sbp_max = ifelse(all(is.na(sbp)), NA, max(sbp, na.rm = TRUE)),
      sbp_range = ifelse(
        all(is.na(sbp)),
        NA,
        max(sbp, na.rm = TRUE) - min(sbp, na.rm = TRUE)
      ),
      dbp_min = ifelse(all(is.na(dbp)), NA, min(dbp, na.rm = TRUE)),
      dbp_max = ifelse(all(is.na(dbp)), NA, max(dbp, na.rm = TRUE)),
      dbp_range = ifelse(
        all(is.na(dbp)),
        NA,
        max(dbp, na.rm = TRUE) - min(dbp, na.rm = TRUE)
      ),
      pulse_min = ifelse(all(is.na(pulse)), NA, min(pulse, na.rm = TRUE)),
      pulse_max = ifelse(all(is.na(pulse)), NA, max(pulse, na.rm = TRUE)),
      pulse_range = ifelse(
        all(is.na(pulse)),
        NA,
        max(pulse, na.rm = TRUE) - min(pulse, na.rm = TRUE)
      )
    ),
    pressure_stats = list(
      mean_pulse_pressure = ifelse(
        all(is.na(pp)),
        NA,
        round(mean(pp, na.rm = TRUE), 1)
      ),
      pulse_pressure_sd = pp_sd
    )
  )
}

classify_row <- function(sbp, dbp) {
  if (is.na(sbp) || is.na(dbp)) {
    return("unknown")
  }
  if (sbp >= 180 || dbp >= 120) {
    return("hypertensive_crisis")
  }
  if (sbp < 90 || dbp < 60) {
    return("hypotensive")
  }
  if (sbp < 120 && dbp < 80) {
    return("normal")
  }
  if (sbp >= 120 && sbp < 130 && dbp < 80) {
    return("elevated")
  }
  if ((sbp >= 130 && sbp < 140) || (dbp >= 80 && dbp < 90)) {
    return("stage1_hypertension")
  }
  if (sbp >= 140 || dbp >= 90) {
    return("stage2_hypertension")
  }
  }

time_of_day_stats <- function(readings_df) {
  parse_time <- function(x) {
    # Support RFC3339 like 2025-09-01T00:00:00.000Z
    suppressWarnings(as.POSIXct(
      gsub("Z$", "", x),
      format = "%Y-%m-%dT%H:%M:%OS",
      tz = "UTC"
    ))
  }
  times <- parse_time(readings_df$reading_time)
  hours <- suppressWarnings(as.integer(format(times, "%H")))
  bucket <- ifelse(!is.na(hours) & hours >= 6 & hours < 12, "morning",
    ifelse(!is.na(hours) & hours >= 12 & hours < 18, "afternoon",
      ifelse(!is.na(hours) & hours >= 18 & hours < 22, "evening",
        ifelse(!is.na(hours), "night", NA)
      )
    )
  )
  agg <- function(idx) {
    if (!any(idx, na.rm = TRUE)) {
      return(list(count = 0, mean_sbp = NA, mean_dbp = NA))
    }
    list(
      count = sum(idx, na.rm = TRUE),
      mean_sbp = mean(
        suppressWarnings(as.numeric(readings_df$sbp[idx])),
        na.rm = TRUE
      ),
      mean_dbp = mean(
        suppressWarnings(as.numeric(readings_df$dbp[idx])),
        na.rm = TRUE
      )
    )
  }
  list(
    morning = agg(bucket == "morning"),
    afternoon = agg(bucket == "afternoon"),
    evening = agg(bucket == "evening"),
    night = agg(bucket == "night")
  )
}

compute_metrics <- function(body) {
  readings <- body$readings
  # Normalize readings to a list of rows even if jsonlite produced a data.frame
  if (is.data.frame(readings)) {
    readings <- split(readings, seq_len(nrow(readings)))
  }
  if (is.null(readings) || length(readings) == 0) {
    return(list(
      success = TRUE,
      analysis_version = coalesce_null(body$analysis_version, "v1.0.0-r"),
      computed_at = as.character(Sys.time()),
      last_input_at = as.character(Sys.time()),
      patient_id = body$patient_id,
      day = coalesce_null(body$day, NULL),
      duration = coalesce_null(body$duration, NULL),
      total_readings = 0,
      basic_stats = list(),
      variability_stats = list(),
      range_stats = list(),
      pressure_stats = list(),
      circadian_stats = list(),
      time_of_day = list(),
      classification_counts = list(
        normal = 0,
        elevated = 0,
        stage1_hypertension = 0,
        stage2_hypertension = 0,
        unknown = 0
      ),
      latest_reading = NULL,
      series = list()
    ))
  }
  df <- data.frame(
    sbp = sapply(readings, function(r) r$sbp),
    dbp = sapply(readings, function(r) r$dbp),
    pulse = sapply(readings, function(r) ifelse(is.null(r$pulse), NA, r$pulse)),
    reading_time = sapply(readings, function(r) r$reading_time),
    stringsAsFactors = FALSE
  )
  bs <- compute_basic_stats(df)
  pressure_stats <- list(
    pulse_pressure = ifelse(
      is.na(bs$basic_stats$mean_sbp) || is.na(bs$basic_stats$mean_dbp),
      NA,
      round((bs$basic_stats$mean_sbp - bs$basic_stats$mean_dbp), 1)
    )
  )
  classes <- sapply(
    seq_len(nrow(df)),
    function(i) {
      classify_row(
        suppressWarnings(as.numeric(df$sbp[i])),
        suppressWarnings(as.numeric(df$dbp[i]))
      )
    }
  )
  counts <- as.list(table(factor(
    classes,
    levels = c(
      "normal", "elevated", "stage1_hypertension", "stage2_hypertension",
      "hypotensive", "hypertensive_crisis", "unknown"
    )
  )))
  names(counts) <- c(
    "normal", "elevated", "stage1_hypertension", "stage2_hypertension",
    "hypotensive", "hypertensive_crisis", "unknown"
  )
  times <- suppressWarnings(as.POSIXct(
    gsub("Z$", "", df$reading_time),
    format = "%Y-%m-%dT%H:%M:%OS",
    tz = "UTC"
  ))
  if (all(is.na(times))) {
    latest <- NULL
  } else {
    latest_idx <- which.max(times)
    latest <- list(
      time = df$reading_time[latest_idx],
      sbp = as.numeric(df$sbp[latest_idx]),
      dbp = as.numeric(df$dbp[latest_idx]),
      map = as.numeric(df$dbp[latest_idx]) + (
        as.numeric(df$sbp[latest_idx]) - as.numeric(df$dbp[latest_idx])
      ) / 3,
      pulse = ifelse(
        is.na(df$pulse[latest_idx]),
        NULL,
        as.numeric(df$pulse[latest_idx])
      )
    )
  }
  series <- lapply(seq_len(nrow(df)), function(i) {
    list(
      time = df$reading_time[i],
      sbp = as.numeric(df$sbp[i]),
      dbp = as.numeric(df$dbp[i]),
      map = as.numeric(df$dbp[i]) + (
        as.numeric(df$sbp[i]) - as.numeric(df$dbp[i])
      ) / 3,
      pulse = ifelse(is.na(df$pulse[i]), NULL, as.numeric(df$pulse[i])),
      classification = classes[i]
    )
  })

  # Control metrics: TIR/TAR and load
  sbp_num <- suppressWarnings(as.numeric(df$sbp))
  dbp_num <- suppressWarnings(as.numeric(df$dbp))
  n_total <- sum(!is.na(sbp_num) & !is.na(dbp_num))
  in_range <- sum((sbp_num < 130) & (dbp_num < 80), na.rm = TRUE)
  above_range <- sum((sbp_num >= 130) | (dbp_num >= 80), na.rm = TRUE)
  sys_load <- sum(sbp_num >= 130, na.rm = TRUE)
  dia_load <- sum(dbp_num >= 80, na.rm = TRUE)
  control_metrics <- list(
    tir_percent = ifelse(
      n_total == 0, NA, round(100 * in_range / n_total, 1)
    ),
    tar_percent = ifelse(
      n_total == 0, NA, round(100 * above_range / n_total, 1)
    ),
    systolic_load_percent = ifelse(
      n_total == 0, NA, round(100 * sys_load / n_total, 1)
    ),
    diastolic_load_percent = ifelse(
      n_total == 0, NA, round(100 * dia_load / n_total, 1)
    )
  )

  # Pulse flags (counts)
  pulse_num <- suppressWarnings(as.numeric(df$pulse))
  pulse_flags <- list(
    brady_lt_40 = sum(pulse_num < 40, na.rm = TRUE),
    brady_lt_50 = sum(pulse_num < 50, na.rm = TRUE),
    brady_lt_60 = sum(pulse_num < 60, na.rm = TRUE),
    tachy_gt_100 = sum(pulse_num > 100, na.rm = TRUE),
    tachy_gt_120 = sum(pulse_num > 120, na.rm = TRUE),
    tachy_gt_150 = sum(pulse_num > 150, na.rm = TRUE)
  )

  # Trend slope (mmHg per day) and 7-day moving average (daily means)
  # Build daily means
  daily_df <- data.frame(
    day = as.Date(times),
    sbp = sbp_num,
    dbp = dbp_num
  )
  daily_agg <- stats::aggregate(
    cbind(sbp, dbp) ~ day,
    data = daily_df,
    FUN = function(x) mean(x, na.rm = TRUE)
  )
  trend <- list(trend_slope_sbp = NA, trend_slope_dbp = NA)
  if (nrow(daily_agg) >= 2) {
    fit_s <- tryCatch(
      stats::lm(
        daily_agg$sbp ~ I(as.numeric(daily_agg$day - min(daily_agg$day)))
      ),
      error = function(e) NULL
    )
    fit_d <- tryCatch(
      stats::lm(
        daily_agg$dbp ~ I(as.numeric(daily_agg$day - min(daily_agg$day)))
      ),
      error = function(e) NULL
    )
    trend$trend_slope_sbp <- if (!is.null(fit_s)) {
      round(coef(fit_s)[[2]], 3)
    } else {
      NA
    }
    trend$trend_slope_dbp <- if (!is.null(fit_d)) {
      round(coef(fit_d)[[2]], 3)
    } else {
      NA
    }
  }
  ma7 <- list(moving_average_7d_sbp = NA, moving_average_7d_dbp = NA)
  if (nrow(daily_agg) > 0) {
    last7 <- tail(daily_agg, n = min(7, nrow(daily_agg)))
    ma7$moving_average_7d_sbp <- round(mean(last7$sbp, na.rm = TRUE), 2)
    ma7$moving_average_7d_dbp <- round(mean(last7$dbp, na.rm = TRUE), 2)
  }

  # 30-day recent trend slopes (if enough span)
  trend30 <- list(trend_30d_sbp = NA, trend_30d_dbp = NA)
  if (nrow(daily_agg) >= 2) {
    cutoff <- max(daily_agg$day, na.rm = TRUE) - 30
    recent <- daily_agg[daily_agg$day >= cutoff, , drop = FALSE]
    if (nrow(recent) >= 2) {
      fit_s2 <- tryCatch(
        stats::lm(
          recent$sbp ~ I(as.numeric(recent$day - min(recent$day)))
        ),
        error = function(e) NULL
      )
      fit_d2 <- tryCatch(
        stats::lm(
          recent$dbp ~ I(as.numeric(recent$day - min(recent$day)))
        ),
        error = function(e) NULL
      )
      trend30$trend_30d_sbp <- if (!is.null(fit_s2)) {
        round(coef(fit_s2)[[2]], 3)
      } else {
        NA
      }
      trend30$trend_30d_dbp <- if (!is.null(fit_d2)) {
        round(coef(fit_d2)[[2]], 3)
      } else {
        NA
      }
    }
  }

  # Day-to-day variability (DDV): SD of daily means
  ddv <- list(ddv_sbp = NA, ddv_dbp = NA)
  if (nrow(daily_agg) >= 2) {
    ddv$ddv_sbp <- round(stats::sd(daily_agg$sbp, na.rm = TRUE), 2)
    ddv$ddv_dbp <- round(stats::sd(daily_agg$dbp, na.rm = TRUE), 2)
  }

  # ARV: mean abs diff of consecutive readings
  arv <- list(arv_sbp = NA, arv_dbp = NA)
  if (
    length(sbp_num[!is.na(sbp_num)]) >= 2 &&
      length(dbp_num[!is.na(dbp_num)]) >= 2
  ) {
    s2 <- sbp_num[!is.na(sbp_num)]
    d2 <- dbp_num[!is.na(dbp_num)]
    if (length(s2) >= 2) arv$arv_sbp <- round(mean(abs(diff(s2))), 2)
    if (length(d2) >= 2) arv$arv_dbp <- round(mean(abs(diff(d2))), 2)
  }

  # Morning surge and morning hypertension (rule-based windows)
  # Morning: 06:00-12:00, Night: 22:00-06:00, Day: 06:00-22:00
  hours <- suppressWarnings(as.integer(format(times, "%H")))
  is_morning <- !is.na(hours) & hours >= 6 & hours < 12
  is_night <- !is.na(hours) & (hours >= 22 | hours < 6)
  is_day <- !is.na(hours) & hours >= 6 & hours < 22
  morning_mean_sbp <- ifelse(
    any(is_morning, na.rm = TRUE),
    mean(sbp_num[is_morning], na.rm = TRUE),
    NA
  )
  morning_mean_dbp <- ifelse(
    any(is_morning, na.rm = TRUE),
    mean(dbp_num[is_morning], na.rm = TRUE),
    NA
  )
  night_min_sbp <- ifelse(
    any(is_night, na.rm = TRUE),
    suppressWarnings(min(sbp_num[is_night], na.rm = TRUE)),
    NA
  )
  night_mean_sbp <- ifelse(
    any(is_night, na.rm = TRUE),
    mean(sbp_num[is_night], na.rm = TRUE),
    NA
  )
  night_mean_dbp <- ifelse(
    any(is_night, na.rm = TRUE),
    mean(dbp_num[is_night], na.rm = TRUE),
    NA
  )
  day_mean_sbp <- ifelse(
    any(is_day, na.rm = TRUE),
    mean(sbp_num[is_day], na.rm = TRUE),
    NA
  )
  day_mean_dbp <- ifelse(
    any(is_day, na.rm = TRUE),
    mean(dbp_num[is_day], na.rm = TRUE),
    NA
  )
  morning_surge <- ifelse(
    is.na(morning_mean_sbp) || is.na(night_min_sbp),
    NA,
    round(morning_mean_sbp - night_min_sbp, 1)
  )
  stms_sbp <- morning_surge
  morning_htn <- ifelse(
    is.na(morning_mean_sbp) && is.na(morning_mean_dbp),
    FALSE,
    (morning_mean_sbp >= 135) || (morning_mean_dbp >= 85)
  )
  # Dipping percent and class based on systolic
  dipping_percent <- ifelse(
    is.na(day_mean_sbp) || is.na(night_mean_sbp) || day_mean_sbp == 0,
    NA,
    round(100 * (day_mean_sbp - night_mean_sbp) / day_mean_sbp, 1)
  )
  dipping_class <- ifelse(is.na(dipping_percent), NA,
    ifelse(dipping_percent < 0, "reverse",
      ifelse(dipping_percent < 10, "non_dipper",
        ifelse(dipping_percent <= 20, "normal",
          "extreme"
        )
      )
    )
  )
  # Nocturnal hypertension flags (absolute and relative)
  nocturnal_htn_absolute <- ifelse(
    is.na(night_mean_sbp) && is.na(night_mean_dbp),
    FALSE,
    (night_mean_sbp >= 120) || (night_mean_dbp >= 70)
  )
  nocturnal_htn_relative <- ifelse(
    is.na(dipping_percent),
    FALSE,
    dipping_percent < 10
  )
  circadian_patterns <- list(
    morning_mean_sbp = ifelse(
      is.nan(morning_mean_sbp),
      NA,
      round(morning_mean_sbp, 1)
    ),
    morning_mean_dbp = ifelse(
      is.nan(morning_mean_dbp),
      NA,
      round(morning_mean_dbp, 1)
    ),
    night_min_sbp = ifelse(is.nan(night_min_sbp), NA, night_min_sbp),
    day_mean_sbp = ifelse(
      is.nan(day_mean_sbp),
      NA,
      round(day_mean_sbp, 1)
    ),
    night_mean_sbp = ifelse(
      is.nan(night_mean_sbp),
      NA,
      round(night_mean_sbp, 1)
    ),
    day_mean_dbp = ifelse(
      is.nan(day_mean_dbp),
      NA,
      round(day_mean_dbp, 1)
    ),
    night_mean_dbp = ifelse(
      is.nan(night_mean_dbp),
      NA,
      round(night_mean_dbp, 1)
    ),
    morning_surge_sbp = stms_sbp,
    morning_hypertension = isTRUE(morning_htn),
    dipping_percent_sbp = dipping_percent,
    dipping_class = dipping_class,
    nocturnal_hypertension_absolute = isTRUE(nocturnal_htn_absolute),
    nocturnal_hypertension_relative = isTRUE(nocturnal_htn_relative)
  )
  # Weekend vs Weekday comparison (means and deltas)
  wdw <- list(
    weekend_weekday_delta_sbp = NA, weekend_weekday_delta_dbp = NA,
    weekend_mean_sbp = NA, weekend_mean_dbp = NA,
    weekday_mean_sbp = NA, weekday_mean_dbp = NA
  )
  if (!all(is.na(times))) {
    dow <- as.POSIXlt(times)$wday
    is_weekend <- !is.na(dow) & (dow == 0 | dow == 6)
    weekend_sbp <- sbp_num[is_weekend]
    weekend_dbp <- dbp_num[is_weekend]
    weekday_sbp <- sbp_num[!is_weekend]
    weekday_dbp <- dbp_num[!is_weekend]
    if (sum(!is.na(weekend_sbp)) >= 1 && sum(!is.na(weekday_sbp)) >= 1) {
      wdw$weekend_mean_sbp <- round(mean(weekend_sbp, na.rm = TRUE), 1)
      wdw$weekday_mean_sbp <- round(mean(weekday_sbp, na.rm = TRUE), 1)
      wdw$weekend_weekday_delta_sbp <- round(
        wdw$weekend_mean_sbp - wdw$weekday_mean_sbp,
        1
      )
    }
    if (sum(!is.na(weekend_dbp)) >= 1 && sum(!is.na(weekday_dbp)) >= 1) {
      wdw$weekend_mean_dbp <- round(mean(weekend_dbp, na.rm = TRUE), 1)
      wdw$weekday_mean_dbp <- round(mean(weekday_dbp, na.rm = TRUE), 1)
      wdw$weekend_weekday_delta_dbp <- round(
        wdw$weekend_mean_dbp - wdw$weekday_mean_dbp,
        1
      )
    }
  }

  # High BP + Low Pulse rule count
  high_bp_low_pulse_count <- sum(
    (
      (sbp_num >= 130 & dbp_num >= 80) |
        (sbp_num >= 140 | dbp_num >= 90)
    ) & (pulse_num < 60),
    na.rm = TRUE
  )
  high_bp_low_pulse <- list(high_bp_low_pulse_count = high_bp_low_pulse_count)

  list(
    success = TRUE,
    analysis_version = coalesce_null(body$analysis_version, "v1.0.0-r"),
    computed_at = as.character(Sys.time()),
    last_input_at = as.character(
      ifelse(
        all(is.na(times)),
        Sys.time(),
        max(times, na.rm = TRUE)
      )
    ),
    patient_id = body$patient_id,
    day = coalesce_null(body$day, NULL),
    duration = coalesce_null(body$duration, NULL),
    total_readings = nrow(df),
    basic_stats = bs$basic_stats,
    variability_stats = bs$variability_stats,
    range_stats = bs$range_stats,
    pressure_stats = modifyList(pressure_stats, bs$pressure_stats),
    circadian_stats = list(
      summary = list(time_of_day_means = TRUE),
      patterns = circadian_patterns,
      comparisons = list(weekend_vs_weekday = wdw),
      context_requirements = list(
        masked_white_coat = list(required = TRUE, status = "data_required"),
        resistant_hypertension = list(
          required = TRUE,
          status = "data_required"
        ),
        orthostatic_hypotension = list(
          required = TRUE,
          status = "data_required"
        )
      )
    ),
    time_of_day = time_of_day_stats(df),
    classification_counts = counts,
    latest_reading = latest,
    series = series,
    combined_stats = c(
      control_metrics, trend, trend30, ma7,
      ddv, arv, pulse_flags, wdw,
      high_bp_low_pulse,
      list(
        dipping_percent_sbp = dipping_percent,
        dipping_class = dipping_class,
        nocturnal_htn_absolute = nocturnal_htn_absolute,
        nocturnal_htn_relative = nocturnal_htn_relative,
        stms_sbp = stms_sbp
      )
    )
  )
}

#* Compute analytics for a specific day
#* @post /v1/bp/compute/day
#* @serializer json list(auto_unbox=TRUE, na='null')
function(req, res) {
  body <- tryCatch(
    jsonlite::fromJSON(
      req$postBody,
      simplifyVector = TRUE
    ),
    error = function(e) NULL
  )
  if (
    is.null(body) || is.null(body$patient_id) ||
      is.null(body$day) || is.null(body$readings)
  ) {
    res$status <- 400
    list(
      success = FALSE,
      error = "patient_id, day, and readings are required"
    )
  }
  out <- tryCatch(
    {
      compute_metrics(body)
    },
    error = function(e) {
      res$status <- 400
      list(
        success = FALSE,
        error = paste0(
          "compute_day_failed: ",
          conditionMessage(e)
        )
      )
    }
  )
  out
}

#* Compute analytics for a duration window
#* @post /v1/bp/compute/duration
#* @serializer json list(auto_unbox=TRUE, na='null')
function(req, res) {
  body <- tryCatch(
    jsonlite::fromJSON(
      req$postBody,
      simplifyVector = TRUE
    ),
    error = function(e) NULL
  )
  if (
    is.null(body) || is.null(body$patient_id) ||
      is.null(body$duration) || is.null(body$readings)
  ) {
    res$status <- 400
    list(
      success = FALSE,
      error = "patient_id, duration, and readings are required"
    )
  }
  out <- tryCatch(
    {
      compute_metrics(body)
    },
    error = function(e) {
      res$status <- 400
      list(
        success = FALSE,
        error = paste0(
          "compute_duration_failed: ",
          conditionMessage(e)
        )
      )
    }
  )
  out
}

#* Batch recompute for multiple patients
#* @post /v1/bp/recompute-batch
#* @serializer json
function(req, res) {
  body <- tryCatch(jsonlite::fromJSON(req$postBody), error = function(e) NULL)
  if (is.null(body) || is.null(body$patientIds)) {
    res$status <- 400
    return(list(error = "patientIds is required"))
  }
  windows <- body$windows
  if (is.null(windows)) {
    windows <- c("1Month", "3Months", "6Months", "1Year", "2Years", "All")
  }
  results <- lapply(body$patientIds, function(pid) {
    list(patientId = pid, status = "queued", windows = windows)
  })
  list(count = length(body$patientIds), results = results)
}
