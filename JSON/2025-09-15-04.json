{"receive_time":"2025-09-15T04:00:00.377Z","event_id":"1904094804899233797","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:00:00.001207+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908800001,"timestamp":"2025-09-15T04:00:00.001207+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":598568641,"host.name":"dev-careportal","time":1757908800001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:00:00.390Z","event_id":"1904094804954132480","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:00:00.006295+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908800006,"timestamp":"2025-09-15T04:00:00.006295+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872707604,"host.name":"dev-careportal","time":1757908800006,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:00:00.390Z","event_id":"1904094804954132481","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:00:00.255769+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908800255,"timestamp":"2025-09-15T04:00:00.255769+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872707604,"host.name":"dev-careportal","time":1757908800255,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:00:00.849Z","event_id":"1904094806878015492","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:00:00.479861+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908800479,"timestamp":"2025-09-15T04:00:00.479861+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315441490,"host.name":"dev-careportal","time":1757908800479,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:00:49.257Z","event_id":"1904095009915883520","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:00:49.200561+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=37bcef90-0c49-90d0-532a-fbf501a41ecb fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908849200,"timestamp":"2025-09-15T04:00:49.200561+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"37bcef90-0c49-90d0-532a-fbf501a41ecb","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757908849200,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:01:49.272Z","event_id":"1904095261638635520","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:01:49.213973+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=50251f59-d3d3-ffcf-eff5-b74016ae8664 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908909213,"timestamp":"2025-09-15T04:01:49.213973+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"50251f59-d3d3-ffcf-eff5-b74016ae8664","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64674597,"host.name":"dev-careportal","time":1757908909213,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:02:49.166Z","event_id":"1904095512852000769","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:02:49.10377+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=74324431-e1c4-9b3d-d1b0-40501d06b55e fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908969103,"timestamp":"2025-09-15T04:02:49.10377+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"74324431-e1c4-9b3d-d1b0-40501d06b55e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":839938714,"host.name":"dev-careportal","time":1757908969103,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:03:49.118Z","event_id":"1904095764308897794","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:03:49.061631+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=61fd8c8f-c7ac-3412-273b-2fd0ce2f485d fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909029061,"timestamp":"2025-09-15T04:03:49.061631+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"61fd8c8f-c7ac-3412-273b-2fd0ce2f485d","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":315516839,"host.name":"dev-careportal","time":1757909029061,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:04:49.240Z","event_id":"1904096016479043585","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:04:49.179606+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cd403f94-76b7-5ae9-a5bc-2e03d7c9d7cb fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909089179,"timestamp":"2025-09-15T04:04:49.179606+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cd403f94-76b7-5ae9-a5bc-2e03d7c9d7cb","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585339536,"host.name":"dev-careportal","time":1757909089179,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:05:00.369Z","event_id":"1904096063156506630","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:05:00.000817+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909100000,"timestamp":"2025-09-15T04:05:00.000817+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":915421937,"host.name":"dev-careportal","time":1757909100000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:05:00.373Z","event_id":"1904096063172358144","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:05:00.001576+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909100001,"timestamp":"2025-09-15T04:05:00.001576+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":919690042,"host.name":"dev-careportal","time":1757909100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:05:00.639Z","event_id":"1904096064289742848","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:05:00.274305+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909100274,"timestamp":"2025-09-15T04:05:00.274305+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751241915,"host.name":"dev-careportal","time":1757909100274,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:05:00.643Z","event_id":"1904096064306085889","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:05:00.265013+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909100265,"timestamp":"2025-09-15T04:05:00.265013+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":317259669,"host.name":"dev-careportal","time":1757909100265,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:05:49.144Z","event_id":"1904096267733176321","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:05:49.062294+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cdeafdb6-23c3-67aa-b2a8-94fca0f683a4 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909149062,"timestamp":"2025-09-15T04:05:49.062294+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cdeafdb6-23c3-67aa-b2a8-94fca0f683a4","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":315568495,"host.name":"dev-careportal","time":1757909149062,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:06:49.122Z","event_id":"1904096519300395008","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:06:49.058868+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5c07148e-ba14-0d0a-15e9-4b07fb36db97 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909209058,"timestamp":"2025-09-15T04:06:49.058868+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5c07148e-ba14-0d0a-15e9-4b07fb36db97","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915578983,"host.name":"dev-careportal","time":1757909209058,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:07:49.324Z","event_id":"1904096771805913088","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:07:49.266043+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a8817448-39e5-992f-186a-0b85afc0f47a fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909269266,"timestamp":"2025-09-15T04:07:49.266043+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a8817448-39e5-992f-186a-0b85afc0f47a","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757909269266,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:08:49.152Z","event_id":"1904097022741401601","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:08:49.092448+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cf5b8c64-67ef-d795-7214-f66956abb058 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909329092,"timestamp":"2025-09-15T04:08:49.092448+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cf5b8c64-67ef-d795-7214-f66956abb058","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757909329092,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:09:49.229Z","event_id":"1904097274723905536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:09:49.171214+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d85a8698-52f6-0e47-2b90-10c27a8df466 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909389171,"timestamp":"2025-09-15T04:09:49.171214+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d85a8698-52f6-0e47-2b90-10c27a8df466","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885802123,"host.name":"dev-careportal","time":1757909389171,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:00.365Z","event_id":"1904097321430372352","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:10:00.001092+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909400001,"timestamp":"2025-09-15T04:10:00.001092+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585200453,"host.name":"dev-careportal","time":1757909400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:00.377Z","event_id":"1904097321480916993","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:10:00.001526+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909400001,"timestamp":"2025-09-15T04:10:00.001526+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751241915,"host.name":"dev-careportal","time":1757909400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:00.656Z","event_id":"1904097322651811852","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:10:00.289475+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909400289,"timestamp":"2025-09-15T04:10:00.289475+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":585998859,"host.name":"dev-careportal","time":1757909400289,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:00.670Z","event_id":"1904097322711343104","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:10:00.285978+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909400285,"timestamp":"2025-09-15T04:10:00.285978+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":317279016,"host.name":"dev-careportal","time":1757909400285,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:16.694Z","event_id":"1904097389920464899","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:10:16.637714+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=1eade353-fc66-12da-248c-a8cb21753943 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909416637,"timestamp":"2025-09-15T04:10:16.637714+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"1eade353-fc66-12da-248c-a8cb21753943","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757909416637,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:17.005Z","event_id":"1904097391223840768","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:10:16.638476+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909416638,"timestamp":"2025-09-15T04:10:16.638476+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":915452677,"host.name":"dev-careportal","time":1757909416638,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:18.041Z","event_id":"1904097395570102273","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:10:17.976578+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=0aa87670-8ba3-8a65-d829-47af3ded483c fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909417976,"timestamp":"2025-09-15T04:10:17.976578+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"0aa87670-8ba3-8a65-d829-47af3ded483c","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":885915710,"host.name":"dev-careportal","time":1757909417976,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:18.343Z","event_id":"1904097396835201024","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:10:17.977217+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909417977,"timestamp":"2025-09-15T04:10:17.977217+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751106977,"host.name":"dev-careportal","time":1757909417977,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:10:49.240Z","event_id":"1904097526428880896","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:10:49.178756+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=07e5206b-808b-63d6-c287-c5b6c199fe03 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909449178,"timestamp":"2025-09-15T04:10:49.178756+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"07e5206b-808b-63d6-c287-c5b6c199fe03","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757909449178,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:11:49.154Z","event_id":"1904097777726013440","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:11:49.094462+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b2898682-e2d7-9219-a4a3-a6fbf9efaab8 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909509094,"timestamp":"2025-09-15T04:11:49.094462+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b2898682-e2d7-9219-a4a3-a6fbf9efaab8","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":752148558,"host.name":"dev-careportal","time":1757909509094,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:12:49.126Z","event_id":"1904098029265866754","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:12:49.065106+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ca8238ed-a2e0-132e-0bad-20093b6cce9e fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909569065,"timestamp":"2025-09-15T04:12:49.065106+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ca8238ed-a2e0-132e-0bad-20093b6cce9e","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751770311,"host.name":"dev-careportal","time":1757909569065,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:13:49.302Z","event_id":"1904098281663647747","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:13:49.246225+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ef8b0fec-b895-f699-6d23-0533a7bf5942 fwd=\"**************\" dyno=web.2 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909629246,"timestamp":"2025-09-15T04:13:49.246225+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ef8b0fec-b895-f699-6d23-0533a7bf5942","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":583158568,"host.name":"dev-careportal","time":1757909629246,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:14:49.169Z","event_id":"1904098532763074560","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:14:49.108317+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e989606d-4ff1-cdcf-a76c-5041cfd47e7b fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909689108,"timestamp":"2025-09-15T04:14:49.108317+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e989606d-4ff1-cdcf-a76c-5041cfd47e7b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583218103,"host.name":"dev-careportal","time":1757909689108,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:15:00.378Z","event_id":"1904098579776147459","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:15:00.001516+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909700001,"timestamp":"2025-09-15T04:15:00.001516+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":63993100,"host.name":"dev-careportal","time":1757909700001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:15:00.379Z","event_id":"1904098579780341760","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:15:00.002048+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909700002,"timestamp":"2025-09-15T04:15:00.002048+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64682922,"host.name":"dev-careportal","time":1757909700002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:15:00.644Z","event_id":"1904098580893491212","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:15:00.280748+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909700280,"timestamp":"2025-09-15T04:15:00.280748+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877157893,"host.name":"dev-careportal","time":1757909700280,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:15:00.648Z","event_id":"1904098580908810255","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:15:00.285658+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757909700285,"timestamp":"2025-09-15T04:15:00.285658+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885590023,"host.name":"dev-careportal","time":1757909700285,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:15:49.125Z","event_id":"1904098784237735936","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:15:49.066562+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=69d4c8de-d7f3-94af-53fd-b92d834f749f fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909749066,"timestamp":"2025-09-15T04:15:49.066562+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"69d4c8de-d7f3-94af-53fd-b92d834f749f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64674597,"host.name":"dev-careportal","time":1757909749066,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:16:49.241Z","event_id":"1904099036380614656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:16:49.180761+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=25766a0a-efac-cfba-70bc-7abbe244f004 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909809180,"timestamp":"2025-09-15T04:16:49.180761+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"25766a0a-efac-cfba-70bc-7abbe244f004","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751948336,"host.name":"dev-careportal","time":1757909809180,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:17:49.180Z","event_id":"1904099287783931904","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:17:49.119655+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=986735c0-30e4-8e41-a471-2a91cb49e08b fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909869119,"timestamp":"2025-09-15T04:17:49.119655+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"986735c0-30e4-8e41-a471-2a91cb49e08b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757909869119,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:18:49.265Z","event_id":"1904099539799089152","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:18:49.202919+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c027f119-d4f1-c850-2759-ee86e61c5c53 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909929202,"timestamp":"2025-09-15T04:18:49.202919+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c027f119-d4f1-c850-2759-ee86e61c5c53","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885802123,"host.name":"dev-careportal","time":1757909929202,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:19:49.357Z","event_id":"1904099791843581952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:19:49.29786+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5f9dc05f-66b0-2180-7a08-f2a09e87d849 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757909989297,"timestamp":"2025-09-15T04:19:49.29786+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5f9dc05f-66b0-2180-7a08-f2a09e87d849","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757909989297,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:20:00.377Z","event_id":"1904099838063353857","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:20:00.00136+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910000001,"timestamp":"2025-09-15T04:20:00.00136+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872508094,"host.name":"dev-careportal","time":1757910000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:20:00.380Z","event_id":"1904099838077374477","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:20:00.001252+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910000001,"timestamp":"2025-09-15T04:20:00.001252+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583366555,"host.name":"dev-careportal","time":1757910000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:20:00.641Z","event_id":"1904099839171330048","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:20:00.273751+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910000273,"timestamp":"2025-09-15T04:20:00.273751+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751259511,"host.name":"dev-careportal","time":1757910000273,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:20:00.647Z","event_id":"1904099839195566080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:20:00.282688+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910000282,"timestamp":"2025-09-15T04:20:00.282688+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":751264138,"host.name":"dev-careportal","time":1757910000282,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:20:49.162Z","event_id":"1904100042682236929","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:20:49.099818+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=16a6286f-1b19-543f-5895-abb6dd6ab15e fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910049099,"timestamp":"2025-09-15T04:20:49.099818+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"16a6286f-1b19-543f-5895-abb6dd6ab15e","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757910049099,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:21:49.155Z","event_id":"1904100294312792064","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:21:49.096187+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1f35c310-86ac-5200-066f-d770813ffde4 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910109096,"timestamp":"2025-09-15T04:21:49.096187+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1f35c310-86ac-5200-066f-d770813ffde4","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387203154,"host.name":"dev-careportal","time":1757910109096,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:22:49.269Z","event_id":"1904100546447544321","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:22:49.207105+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=340aee81-15a3-8561-793c-b97622c750e7 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910169207,"timestamp":"2025-09-15T04:22:49.207105+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"340aee81-15a3-8561-793c-b97622c750e7","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757910169207,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:23:49.193Z","event_id":"1904100797787525120","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:23:49.133401+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1f0536ab-14aa-ba2a-7a30-4b15f207dfc4 fwd=\"**************\" dyno=web.2 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910229133,"timestamp":"2025-09-15T04:23:49.133401+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1f0536ab-14aa-ba2a-7a30-4b15f207dfc4","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":585709848,"host.name":"dev-careportal","time":1757910229133,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:24:49.230Z","event_id":"1904101049601294355","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:24:49.16979+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4dd51d85-d9cb-c2f5-77d5-5673c49be970 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910289169,"timestamp":"2025-09-15T04:24:49.16979+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4dd51d85-d9cb-c2f5-77d5-5673c49be970","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757910289169,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:25:00.364Z","event_id":"1904101096299790336","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:25:00.001078+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910300001,"timestamp":"2025-09-15T04:25:00.001078+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":752124449,"host.name":"dev-careportal","time":1757910300001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:25:00.378Z","event_id":"1904101096358711296","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:25:00.001578+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910300001,"timestamp":"2025-09-15T04:25:00.001578+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":872707604,"host.name":"dev-careportal","time":1757910300001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:25:00.674Z","event_id":"1904101097600225280","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:25:00.310416+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910300310,"timestamp":"2025-09-15T04:25:00.310416+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64384602,"host.name":"dev-careportal","time":1757910300310,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:25:00.730Z","event_id":"1904101097836396544","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:25:00.364464+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910300364,"timestamp":"2025-09-15T04:25:00.364464+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":598432550,"host.name":"dev-careportal","time":1757910300364,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:25:49.271Z","event_id":"1904101301431533568","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:25:49.211273+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d1bd725d-6776-84a9-c8a5-e14ab77fade2 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910349211,"timestamp":"2025-09-15T04:25:49.211273+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d1bd725d-6776-84a9-c8a5-e14ab77fade2","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583221001,"host.name":"dev-careportal","time":1757910349211,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:26:24.683Z","event_id":"1904101449960628224","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:26:24.621165+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=a051a085-c962-7177-389a-701dd644adec fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910384621,"timestamp":"2025-09-15T04:26:24.621165+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"a051a085-c962-7177-389a-701dd644adec","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":583218103,"host.name":"dev-careportal","time":1757910384621,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:26:24.987Z","event_id":"1904101451234922496","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:26:24.621968+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910384621,"timestamp":"2025-09-15T04:26:24.621968+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":56582585,"host.name":"dev-careportal","time":1757910384621,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:26:49.192Z","event_id":"1904101552757706752","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:26:49.13307+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=524f4b98-b107-9f40-2dc6-ba16ac9bf313 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910409133,"timestamp":"2025-09-15T04:26:49.13307+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"524f4b98-b107-9f40-2dc6-ba16ac9bf313","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":597284469,"host.name":"dev-careportal","time":1757910409133,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:27:49.229Z","event_id":"1904101804572520448","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:27:49.167139+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bc74f425-887a-f072-4ec1-100242bde4e0 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910469167,"timestamp":"2025-09-15T04:27:49.167139+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bc74f425-887a-f072-4ec1-100242bde4e0","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583222442,"host.name":"dev-careportal","time":1757910469167,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:28:49.275Z","event_id":"1904102056423403520","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:28:49.214583+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8b96988e-0060-950a-9233-6e523fb7fed2 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910529214,"timestamp":"2025-09-15T04:28:49.214583+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8b96988e-0060-950a-9233-6e523fb7fed2","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":918867257,"host.name":"dev-careportal","time":1757910529214,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:29:49.114Z","event_id":"1904102307405987884","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:29:49.056385+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c039e472-cfcb-2a88-2c00-86130e169bec fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910589056,"timestamp":"2025-09-15T04:29:49.056385+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c039e472-cfcb-2a88-2c00-86130e169bec","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751902213,"host.name":"dev-careportal","time":1757910589056,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:30:00.373Z","event_id":"1904102354630230017","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:30:00.007165+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910600007,"timestamp":"2025-09-15T04:30:00.007165+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":316010271,"host.name":"dev-careportal","time":1757910600007,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:30:00.387Z","event_id":"1904102354688950280","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:30:00.001482+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910600001,"timestamp":"2025-09-15T04:30:00.001482+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877157893,"host.name":"dev-careportal","time":1757910600001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:30:00.665Z","event_id":"1904102355854393347","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:30:00.303058+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910600303,"timestamp":"2025-09-15T04:30:00.303058+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64048138,"host.name":"dev-careportal","time":1757910600303,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:30:00.706Z","event_id":"1904102356024520709","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:30:00.340861+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910600340,"timestamp":"2025-09-15T04:30:00.340861+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":877154211,"host.name":"dev-careportal","time":1757910600340,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:30:49.230Z","event_id":"1904102559551156225","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:30:49.167614+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=fe75f99c-4d65-7162-96ae-16556f9805c2 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910649167,"timestamp":"2025-09-15T04:30:49.167614+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"fe75f99c-4d65-7162-96ae-16556f9805c2","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915420555,"host.name":"dev-careportal","time":1757910649167,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:31:49.279Z","event_id":"1904102811413811203","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:31:49.218437+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=34aab27c-b566-7b2a-b4c0-001843836c2e fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910709218,"timestamp":"2025-09-15T04:31:49.218437+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"34aab27c-b566-7b2a-b4c0-001843836c2e","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757910709218,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:32:49.171Z","event_id":"1904103062619750400","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:32:49.106982+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1716e4fd-8250-d60e-e0f0-55aaca8f3d9c fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910769106,"timestamp":"2025-09-15T04:32:49.106982+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1716e4fd-8250-d60e-e0f0-55aaca8f3d9c","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":315564962,"host.name":"dev-careportal","time":1757910769106,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:33:49.143Z","event_id":"1904103314159652864","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:33:49.084001+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8cc76129-400d-f8ce-f76b-eabd651b3ab4 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910829084,"timestamp":"2025-09-15T04:33:49.084001+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8cc76129-400d-f8ce-f76b-eabd651b3ab4","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":918867257,"host.name":"dev-careportal","time":1757910829084,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:34:49.260Z","event_id":"1904103566308257792","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:34:49.199308+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8ecab846-af6d-22c0-3514-abe9e43ff2f7 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910889199,"timestamp":"2025-09-15T04:34:49.199308+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8ecab846-af6d-22c0-3514-abe9e43ff2f7","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65304058,"host.name":"dev-careportal","time":1757910889199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:35:00.366Z","event_id":"1904103612890615808","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:35:00.001479+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910900001,"timestamp":"2025-09-15T04:35:00.001479+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":598639734,"host.name":"dev-careportal","time":1757910900001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:35:00.366Z","event_id":"1904103612890615809","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:35:00.228357+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910900228,"timestamp":"2025-09-15T04:35:00.228357+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":598639734,"host.name":"dev-careportal","time":1757910900228,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:35:00.368Z","event_id":"1904103612900257792","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:35:00.001101+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910900001,"timestamp":"2025-09-15T04:35:00.001101+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915657614,"host.name":"dev-careportal","time":1757910900001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:35:00.368Z","event_id":"1904103612900257793","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:35:00.23339+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757910900233,"timestamp":"2025-09-15T04:35:00.23339+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915657614,"host.name":"dev-careportal","time":1757910900233,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:35:49.114Z","event_id":"1904103817356185600","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:35:49.0553+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c17b4dbc-9ce2-94d8-f776-5d7e2aa69319 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757910949055,"timestamp":"2025-09-15T04:35:49.0553+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c17b4dbc-9ce2-94d8-f776-5d7e2aa69319","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":3091819137,"host.name":"dev-careportal","time":1757910949055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:36:49.287Z","event_id":"1904104069739855872","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:36:49.226628+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5640656a-fd6e-105e-318c-d21995a44815 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911009226,"timestamp":"2025-09-15T04:36:49.226628+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5640656a-fd6e-105e-318c-d21995a44815","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585794316,"host.name":"dev-careportal","time":1757911009226,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:37:49.278Z","event_id":"1904104321359056897","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:37:49.21846+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=afa089c7-e97a-54c5-1644-ba11cdb5d4bf fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911069218,"timestamp":"2025-09-15T04:37:49.21846+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"afa089c7-e97a-54c5-1644-ba11cdb5d4bf","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757911069218,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:38:49.158Z","event_id":"1904104572512858113","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:38:49.096876+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d75fa506-5f70-3f2f-c5bb-5b7c5e313140 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911129096,"timestamp":"2025-09-15T04:38:49.096876+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d75fa506-5f70-3f2f-c5bb-5b7c5e313140","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757911129096,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:39:49.177Z","event_id":"1904104824253407234","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:39:49.115694+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=145e87de-ffb2-8fe9-1350-119bdd207c83 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911189115,"timestamp":"2025-09-15T04:39:49.115694+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"145e87de-ffb2-8fe9-1350-119bdd207c83","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757911189115,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:40:00.366Z","event_id":"1904104871183474704","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:40:00.001487+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911200001,"timestamp":"2025-09-15T04:40:00.001487+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751044126,"host.name":"dev-careportal","time":1757911200001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:40:00.373Z","event_id":"1904104871212457984","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:40:00.003061+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911200003,"timestamp":"2025-09-15T04:40:00.003061+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916450488,"host.name":"dev-careportal","time":1757911200003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:40:00.708Z","event_id":"1904104872618119171","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:40:00.333112+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911200333,"timestamp":"2025-09-15T04:40:00.333112+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65005012,"host.name":"dev-careportal","time":1757911200333,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:40:00.730Z","event_id":"1904104872709996544","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:40:00.368322+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911200368,"timestamp":"2025-09-15T04:40:00.368322+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751193404,"host.name":"dev-careportal","time":1757911200368,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:40:49.240Z","event_id":"1904105076175888384","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:40:49.182642+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=52c8affe-69e0-2553-d828-05623a8add32 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911249182,"timestamp":"2025-09-15T04:40:49.182642+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"52c8affe-69e0-2553-d828-05623a8add32","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757911249182,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:41:49.148Z","event_id":"1904105327447101442","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:41:49.081092+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=05944cbe-f9fa-83fb-5d20-a65eff98cfa0 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911309081,"timestamp":"2025-09-15T04:41:49.081092+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"05944cbe-f9fa-83fb-5d20-a65eff98cfa0","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751902213,"host.name":"dev-careportal","time":1757911309081,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:42:49.103Z","event_id":"1904105578916089856","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:42:49.043588+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4781fc97-c29a-2995-e30a-89b201a694d3 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911369043,"timestamp":"2025-09-15T04:42:49.043588+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4781fc97-c29a-2995-e30a-89b201a694d3","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387203154,"host.name":"dev-careportal","time":1757911369043,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:43:49.232Z","event_id":"1904105831117033472","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:43:49.170333+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8d4d51fa-1f59-6292-c083-65ee666b35b5 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911429170,"timestamp":"2025-09-15T04:43:49.170333+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8d4d51fa-1f59-6292-c083-65ee666b35b5","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":877134772,"host.name":"dev-careportal","time":1757911429170,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:44:49.149Z","event_id":"1904106082424549376","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:44:49.089693+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e198dd1c-a8dc-0577-84e4-2f7318e8be46 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911489089,"timestamp":"2025-09-15T04:44:49.089693+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e198dd1c-a8dc-0577-84e4-2f7318e8be46","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64674597,"host.name":"dev-careportal","time":1757911489089,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:45:00.366Z","event_id":"1904106129474867200","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***************","sw.remote.ip":"***************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:45:00.001768+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911500001,"timestamp":"2025-09-15T04:45:00.001768+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":3438077126,"host.name":"dev-careportal","time":1757911500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:45:00.374Z","event_id":"1904106129506734080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:45:00.001499+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911500001,"timestamp":"2025-09-15T04:45:00.001499+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64682922,"host.name":"dev-careportal","time":1757911500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:45:00.374Z","event_id":"1904106129506734081","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:45:00.250174+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911500250,"timestamp":"2025-09-15T04:45:00.250174+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64682922,"host.name":"dev-careportal","time":1757911500250,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:45:00.652Z","event_id":"1904106130673750021","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:45:00.281937+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911500281,"timestamp":"2025-09-15T04:45:00.281937+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64300867,"host.name":"dev-careportal","time":1757911500281,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:45:49.242Z","event_id":"1904106334474981376","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:45:49.185703+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=eb595af1-08f4-6662-ca4b-2b67910c62ff fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911549185,"timestamp":"2025-09-15T04:45:49.185703+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"eb595af1-08f4-6662-ca4b-2b67910c62ff","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916874444,"host.name":"dev-careportal","time":1757911549185,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:46:49.260Z","event_id":"1904106586208403457","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:46:49.20025+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5d90d30f-985d-4434-a935-c78af89c42fe fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911609200,"timestamp":"2025-09-15T04:46:49.20025+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5d90d30f-985d-4434-a935-c78af89c42fe","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":877223021,"host.name":"dev-careportal","time":1757911609200,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:47:49.186Z","event_id":"1904106837556965469","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:47:49.125308+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f22cdef2-bd88-93aa-320c-55913766c0cb fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911669125,"timestamp":"2025-09-15T04:47:49.125308+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f22cdef2-bd88-93aa-320c-55913766c0cb","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598626444,"host.name":"dev-careportal","time":1757911669125,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:48:49.131Z","event_id":"1904107088984629248","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:48:49.070467+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ac392d50-0304-6db1-adff-e39aaa18ce5c fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911729070,"timestamp":"2025-09-15T04:48:49.070467+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ac392d50-0304-6db1-adff-e39aaa18ce5c","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757911729070,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:49:49.276Z","event_id":"1904107341250838528","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:49:49.216457+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=be7ac8df-bb4e-da77-a83d-03cec692f1ed fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911789216,"timestamp":"2025-09-15T04:49:49.216457+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"be7ac8df-bb4e-da77-a83d-03cec692f1ed","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583222442,"host.name":"dev-careportal","time":1757911789216,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:50:00.370Z","event_id":"1904107387782651907","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:50:00.005523+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911800005,"timestamp":"2025-09-15T04:50:00.005523+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315969449,"host.name":"dev-careportal","time":1757911800005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:50:00.376Z","event_id":"1904107387805790224","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:50:00.004896+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911800004,"timestamp":"2025-09-15T04:50:00.004896+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":877246267,"host.name":"dev-careportal","time":1757911800004,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:50:00.376Z","event_id":"1904107387805790225","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:50:00.248539+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911800248,"timestamp":"2025-09-15T04:50:00.248539+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":877246267,"host.name":"dev-careportal","time":1757911800248,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:50:00.624Z","event_id":"1904107388845928448","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:50:00.255997+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757911800255,"timestamp":"2025-09-15T04:50:00.255997+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":915657614,"host.name":"dev-careportal","time":1757911800255,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:50:49.139Z","event_id":"1904107592334856194","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:50:49.080147+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ad40edcb-0b64-9db5-9114-3e2a3c59f7f2 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911849080,"timestamp":"2025-09-15T04:50:49.080147+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ad40edcb-0b64-9db5-9114-3e2a3c59f7f2","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585674247,"host.name":"dev-careportal","time":1757911849080,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:51:49.103Z","event_id":"1904107843841097915","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:51:49.046535+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=19413e06-ee87-4652-1944-e671753b5fd1 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911909046,"timestamp":"2025-09-15T04:51:49.046535+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"19413e06-ee87-4652-1944-e671753b5fd1","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916874444,"host.name":"dev-careportal","time":1757911909046,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:52:49.250Z","event_id":"1904108096115216385","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:52:49.189024+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f8536a1b-b2f4-405a-65de-205d822dac3f fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757911969189,"timestamp":"2025-09-15T04:52:49.189024+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f8536a1b-b2f4-405a-65de-205d822dac3f","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":3091819137,"host.name":"dev-careportal","time":1757911969189,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:53:49.118Z","event_id":"1904108347221098500","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:53:49.061593+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8376ece9-e277-e606-24f3-a505a102d5fa fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912029061,"timestamp":"2025-09-15T04:53:49.061593+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8376ece9-e277-e606-24f3-a505a102d5fa","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387383325,"host.name":"dev-careportal","time":1757912029061,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:54:49.134Z","event_id":"1904108598946275328","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:54:49.069318+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c19a23c3-c400-2494-ecd9-3b43e79290a6 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912089069,"timestamp":"2025-09-15T04:54:49.069318+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c19a23c3-c400-2494-ecd9-3b43e79290a6","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757912089069,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:55:00.378Z","event_id":"1904108646106628100","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:55:00.005077+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912100005,"timestamp":"2025-09-15T04:55:00.005077+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65067227,"host.name":"dev-careportal","time":1757912100005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:55:00.379Z","event_id":"1904108646109941760","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:55:00.0015+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912100001,"timestamp":"2025-09-15T04:55:00.0015+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751264138,"host.name":"dev-careportal","time":1757912100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:55:00.636Z","event_id":"1904108647189331968","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T04:55:00.264824+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912100264,"timestamp":"2025-09-15T04:55:00.264824+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751251959,"host.name":"dev-careportal","time":1757912100264,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:55:00.639Z","event_id":"1904108647200043009","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:55:00.272858+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912100272,"timestamp":"2025-09-15T04:55:00.272858+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872577865,"host.name":"dev-careportal","time":1757912100272,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:55:49.273Z","event_id":"1904108851185872896","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:55:49.205351+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=552f63f7-4373-42b0-e88a-2d921c0bfd84 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912149205,"timestamp":"2025-09-15T04:55:49.205351+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"552f63f7-4373-42b0-e88a-2d921c0bfd84","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598322888,"host.name":"dev-careportal","time":1757912149205,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:56:49.140Z","event_id":"1904109102285680643","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:56:49.077394+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1fb3dad9-c791-6405-95ef-747553078eeb fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912209077,"timestamp":"2025-09-15T04:56:49.077394+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1fb3dad9-c791-6405-95ef-747553078eeb","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757912209077,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:57:49.219Z","event_id":"1904109354277498887","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:57:49.156656+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=61947c92-ba8e-39ea-1e35-d8358207e4c3 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912269156,"timestamp":"2025-09-15T04:57:49.156656+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"61947c92-ba8e-39ea-1e35-d8358207e4c3","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":752560270,"host.name":"dev-careportal","time":1757912269156,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:58:49.311Z","event_id":"1904109606321614848","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:58:49.254343+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9fd930c2-faea-4a93-d10c-63883e267881 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912329254,"timestamp":"2025-09-15T04:58:49.254343+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9fd930c2-faea-4a93-d10c-63883e267881","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757912329254,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:58:54.333Z","event_id":"1904109627384303618","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:58:54.273781+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=bcc0d48d-0bb8-5c6c-aaf5-e0bed6407787 fwd=\"*************\" dyno=web.1 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912334273,"timestamp":"2025-09-15T04:58:54.273781+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"bcc0d48d-0bb8-5c6c-aaf5-e0bed6407787","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757912334273,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:58:54.640Z","event_id":"1904109628673430004","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T04:58:54.274721+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912334274,"timestamp":"2025-09-15T04:58:54.274721+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64384602,"host.name":"dev-careportal","time":1757912334274,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T04:59:49.115Z","event_id":"1904109857157754880","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T04:59:49.056098+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=98ea33da-7c99-d5be-76ae-bfe4dfb1e051 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912389056,"timestamp":"2025-09-15T04:59:49.056098+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"98ea33da-7c99-d5be-76ae-bfe4dfb1e051","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757912389056,"source_name":"dev-careportal"}
