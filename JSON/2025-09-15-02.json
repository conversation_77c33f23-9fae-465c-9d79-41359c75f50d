{"receive_time":"2025-09-15T02:00:00.373Z","event_id":"1904064605892407296","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:00:00.001456+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901600001,"timestamp":"2025-09-15T02:00:00.001456+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":750963055,"host.name":"dev-careportal","time":1757901600001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:00:00.385Z","event_id":"1904064605944377344","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:00:00.00522+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901600005,"timestamp":"2025-09-15T02:00:00.00522+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1264953453,"host.name":"dev-careportal","time":1757901600005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:00:00.710Z","event_id":"1904064607307509761","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:00:00.342929+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901600342,"timestamp":"2025-09-15T02:00:00.342929+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":885662515,"host.name":"dev-careportal","time":1757901600342,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:00:00.736Z","event_id":"1904064607415508992","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:00:00.374033+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901600374,"timestamp":"2025-09-15T02:00:00.374033+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583366555,"host.name":"dev-careportal","time":1757901600374,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:00:49.107Z","event_id":"1904064810299449371","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:00:49.046503+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=35d749a1-6315-7c83-9fa8-61e9772fe6cc fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901649046,"timestamp":"2025-09-15T02:00:49.046503+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"35d749a1-6315-7c83-9fa8-61e9772fe6cc","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915462169,"host.name":"dev-careportal","time":1757901649046,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:01:49.243Z","event_id":"1904065062528290816","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:01:49.182987+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8a97c46b-f38c-e1db-8a2d-bcb2ac8b9eeb fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901709182,"timestamp":"2025-09-15T02:01:49.182987+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8a97c46b-f38c-e1db-8a2d-bcb2ac8b9eeb","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757901709182,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:02:49.200Z","event_id":"1904065314004537344","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:02:49.134156+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=dd42de2c-551e-87f9-9f95-900defea120b fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901769134,"timestamp":"2025-09-15T02:02:49.134156+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"dd42de2c-551e-87f9-9f95-900defea120b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757901769134,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:03:41.006Z","event_id":"1904065531296198656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:03:40.944306+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=32b62b72-0036-fc84-78b1-b73e659bc4dd fwd=\"*************\" dyno=web.2 connect=0ms service=55004ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901820944,"timestamp":"2025-09-15T02:03:40.944306+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"32b62b72-0036-fc84-78b1-b73e659bc4dd","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55004ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55004},"syslog_priority":134,"sender_ip":64801173,"host.name":"dev-careportal","time":1757901820944,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:03:41.313Z","event_id":"1904065532583182336","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:03:40.946082+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901820946,"timestamp":"2025-09-15T02:03:40.946082+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1679314106,"host.name":"dev-careportal","time":1757901820946,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:03:49.261Z","event_id":"1904065565920178176","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:03:49.202876+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=df23dc69-f36e-0d93-a0ad-c5ed90915636 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901829202,"timestamp":"2025-09-15T02:03:49.202876+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"df23dc69-f36e-0d93-a0ad-c5ed90915636","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757901829202,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:04:49.299Z","event_id":"1904065817737891840","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:04:49.24099+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b4e64e59-d359-904b-88b6-50679277690d fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901889240,"timestamp":"2025-09-15T02:04:49.24099+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b4e64e59-d359-904b-88b6-50679277690d","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872446568,"host.name":"dev-careportal","time":1757901889240,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:05:00.364Z","event_id":"1904065864147497008","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:05:00.001121+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901900001,"timestamp":"2025-09-15T02:05:00.001121+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585653277,"host.name":"dev-careportal","time":1757901900001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:05:00.364Z","event_id":"1904065864147497009","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:05:00.247833+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901900247,"timestamp":"2025-09-15T02:05:00.247833+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585653277,"host.name":"dev-careportal","time":1757901900247,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:05:00.364Z","event_id":"1904065864147497010","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:05:00.003555+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901900003,"timestamp":"2025-09-15T02:05:00.003555+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":585653277,"host.name":"dev-careportal","time":1757901900003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:05:00.626Z","event_id":"1904065865244176485","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:05:00.259317+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901900259,"timestamp":"2025-09-15T02:05:00.259317+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751044126,"host.name":"dev-careportal","time":1757901900259,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:05:49.186Z","event_id":"1904066068921700355","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:05:49.126618+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e72f1387-a064-c309-108a-e55f4d7bd944 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901949126,"timestamp":"2025-09-15T02:05:49.126618+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e72f1387-a064-c309-108a-e55f4d7bd944","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757901949126,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:06:49.245Z","event_id":"1904066320827121671","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:06:49.187444+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c1b46820-2ece-c6f3-e1df-57815f2daaa7 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902009187,"timestamp":"2025-09-15T02:06:49.187444+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c1b46820-2ece-c6f3-e1df-57815f2daaa7","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757902009187,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:07:49.254Z","event_id":"1904066572521861121","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:07:49.192796+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8c878283-260b-85af-0f67-8a1eaebebb0b fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902069192,"timestamp":"2025-09-15T02:07:49.192796+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8c878283-260b-85af-0f67-8a1eaebebb0b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916485046,"host.name":"dev-careportal","time":1757902069192,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:08:49.186Z","event_id":"1904066823896711170","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:08:49.125138+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c09d7f51-e2bb-0922-9e7d-bbaa49329626 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902129125,"timestamp":"2025-09-15T02:08:49.125138+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c09d7f51-e2bb-0922-9e7d-bbaa49329626","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757902129125,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:09:49.183Z","event_id":"1904067075542552576","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:09:49.112967+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2fda25f0-ca85-8a97-f897-fe4bec0352ab fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902189112,"timestamp":"2025-09-15T02:09:49.112967+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2fda25f0-ca85-8a97-f897-fe4bec0352ab","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916874444,"host.name":"dev-careportal","time":1757902189112,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:10:00.367Z","event_id":"1904067122451669016","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:10:00.00133+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902200001,"timestamp":"2025-09-15T02:10:00.00133+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":752124449,"host.name":"dev-careportal","time":1757902200001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:10:00.411Z","event_id":"1904067122634559488","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:10:00.003845+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902200003,"timestamp":"2025-09-15T02:10:00.003845+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916613287,"host.name":"dev-careportal","time":1757902200003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:10:00.411Z","event_id":"1904067122634559489","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:10:00.251364+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902200251,"timestamp":"2025-09-15T02:10:00.251364+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916613287,"host.name":"dev-careportal","time":1757902200251,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:10:00.653Z","event_id":"1904067123650428928","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:10:00.282066+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902200282,"timestamp":"2025-09-15T02:10:00.282066+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":316108344,"host.name":"dev-careportal","time":1757902200282,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:10:49.253Z","event_id":"1904067327494393859","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:10:49.194985+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5ef99bc3-0e6c-14a5-64c8-9c85d5bfea1f fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902249194,"timestamp":"2025-09-15T02:10:49.194985+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5ef99bc3-0e6c-14a5-64c8-9c85d5bfea1f","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757902249194,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:11:49.113Z","event_id":"1904067578563792965","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:11:49.055496+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ef0c0635-5350-080d-88df-3a4077fc8a71 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902309055,"timestamp":"2025-09-15T02:11:49.055496+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ef0c0635-5350-080d-88df-3a4077fc8a71","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757902309055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:12:49.168Z","event_id":"1904067830452670464","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:12:49.105176+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3dc9129d-eda3-cd03-a30c-bdd5252ddf7e fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902369105,"timestamp":"2025-09-15T02:12:49.105176+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3dc9129d-eda3-cd03-a30c-bdd5252ddf7e","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757902369105,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:13:49.241Z","event_id":"1904068082417094656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:13:49.183365+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f1e593ca-7ad0-5fcc-ac08-dcdc1ea4d87f fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902429183,"timestamp":"2025-09-15T02:13:49.183365+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f1e593ca-7ad0-5fcc-ac08-dcdc1ea4d87f","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757902429183,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:14:49.150Z","event_id":"1904068333695553536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:14:49.089466+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=66183047-7ea7-6b3b-b31c-6bf2943d1f36 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902489089,"timestamp":"2025-09-15T02:14:49.089466+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"66183047-7ea7-6b3b-b31c-6bf2943d1f36","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":840027160,"host.name":"dev-careportal","time":1757902489089,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:15:00.375Z","event_id":"1904068380774346753","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:15:00.001871+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902500001,"timestamp":"2025-09-15T02:15:00.001871+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64300867,"host.name":"dev-careportal","time":1757902500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:15:00.379Z","event_id":"1904068380793004032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:15:00.001541+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902500001,"timestamp":"2025-09-15T02:15:00.001541+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":55684874,"host.name":"dev-careportal","time":1757902500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:15:00.641Z","event_id":"1904068381890031616","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:15:00.278598+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902500278,"timestamp":"2025-09-15T02:15:00.278598+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64617088,"host.name":"dev-careportal","time":1757902500278,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:15:00.713Z","event_id":"1904068382193709080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:15:00.337173+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902500337,"timestamp":"2025-09-15T02:15:00.337173+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":55728378,"host.name":"dev-careportal","time":1757902500337,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:15:49.126Z","event_id":"1904068585251278853","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:15:49.065039+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=88d9f8c8-f5f0-c213-86d0-c2c55acae2d9 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902549065,"timestamp":"2025-09-15T02:15:49.065039+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"88d9f8c8-f5f0-c213-86d0-c2c55acae2d9","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751948336,"host.name":"dev-careportal","time":1757902549065,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:16:49.268Z","event_id":"1904068837504151555","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:16:49.209013+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1b241af6-bc86-ffb5-6a7d-b91013a424ce fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902609209,"timestamp":"2025-09-15T02:16:49.209013+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1b241af6-bc86-ffb5-6a7d-b91013a424ce","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757902609209,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:17:49.179Z","event_id":"1904069088790257664","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:17:49.116868+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3f547739-9259-6568-62ac-dc2954952a13 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902669116,"timestamp":"2025-09-15T02:17:49.116868+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3f547739-9259-6568-62ac-dc2954952a13","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583221001,"host.name":"dev-careportal","time":1757902669116,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:18:49.244Z","event_id":"1904069340721774592","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:18:49.180656+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b6c24da7-a776-936a-bc02-0940e28ef4ba fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902729180,"timestamp":"2025-09-15T02:18:49.180656+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b6c24da7-a776-936a-bc02-0940e28ef4ba","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757902729180,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:19:49.260Z","event_id":"1904069592447729665","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:19:49.188453+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c6fce53a-2ee7-8795-6e85-faf68ecb24b2 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902789188,"timestamp":"2025-09-15T02:19:49.188453+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c6fce53a-2ee7-8795-6e85-faf68ecb24b2","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65353257,"host.name":"dev-careportal","time":1757902789188,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:20:00.376Z","event_id":"1904069639071817908","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:20:00.001901+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902800001,"timestamp":"2025-09-15T02:20:00.001901+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":317279016,"host.name":"dev-careportal","time":1757902800001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:20:00.376Z","event_id":"1904069639071817909","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:20:00.229485+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902800229,"timestamp":"2025-09-15T02:20:00.229485+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":317279016,"host.name":"dev-careportal","time":1757902800229,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:20:00.377Z","event_id":"1904069639074516992","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:20:00.002875+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902800002,"timestamp":"2025-09-15T02:20:00.002875+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1796681299,"host.name":"dev-careportal","time":1757902800002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:20:00.377Z","event_id":"1904069639074516993","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:20:00.247322+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757902800247,"timestamp":"2025-09-15T02:20:00.247322+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1796681299,"host.name":"dev-careportal","time":1757902800247,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:20:49.162Z","event_id":"1904069843694637060","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:20:49.103146+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c6752bcb-887d-ef33-dca8-82edddfe7348 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902849103,"timestamp":"2025-09-15T02:20:49.103146+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c6752bcb-887d-ef33-dca8-82edddfe7348","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757902849103,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:21:49.237Z","event_id":"1904070095667449856","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:21:49.176982+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f8425ddb-6257-b190-4d81-0626aef5d055 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902909176,"timestamp":"2025-09-15T02:21:49.176982+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f8425ddb-6257-b190-4d81-0626aef5d055","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757902909176,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:22:49.261Z","event_id":"1904070347424821248","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:22:49.199259+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f5b59ab1-cd2c-e3ae-cf1b-ec1b6ef49b53 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757902969199,"timestamp":"2025-09-15T02:22:49.199259+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f5b59ab1-cd2c-e3ae-cf1b-ec1b6ef49b53","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757902969199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:23:49.190Z","event_id":"1904070598784675840","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:23:49.125998+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f093cadd-7e92-6eea-9eba-4fe76bef039f fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903029125,"timestamp":"2025-09-15T02:23:49.125998+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f093cadd-7e92-6eea-9eba-4fe76bef039f","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757903029125,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:24:49.139Z","event_id":"1904070850230472704","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:24:49.079567+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e5a25911-f00e-15cc-4a41-8f74ef0eb575 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903089079,"timestamp":"2025-09-15T02:24:49.079567+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e5a25911-f00e-15cc-4a41-8f74ef0eb575","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585709848,"host.name":"dev-careportal","time":1757903089079,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:25:00.365Z","event_id":"1904070897316384835","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:25:00.002221+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903100002,"timestamp":"2025-09-15T02:25:00.002221+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585735450,"host.name":"dev-careportal","time":1757903100002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:25:00.373Z","event_id":"1904070897350230016","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:25:00.005287+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903100005,"timestamp":"2025-09-15T02:25:00.005287+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":598639734,"host.name":"dev-careportal","time":1757903100005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:25:00.661Z","event_id":"1904070898557988865","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:25:00.293576+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903100293,"timestamp":"2025-09-15T02:25:00.293576+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":878067448,"host.name":"dev-careportal","time":1757903100293,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:25:00.668Z","event_id":"1904070898587377664","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:25:00.300577+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903100300,"timestamp":"2025-09-15T02:25:00.300577+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":919368764,"host.name":"dev-careportal","time":1757903100300,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:25:49.264Z","event_id":"1904071102412656640","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:25:49.203033+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=50127957-8233-0178-7b77-a20f61605e29 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903149203,"timestamp":"2025-09-15T02:25:49.203033+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"50127957-8233-0178-7b77-a20f61605e29","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583221001,"host.name":"dev-careportal","time":1757903149203,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:26:49.133Z","event_id":"1904071353522917379","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:26:49.073545+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c9bd67d4-f0a5-257c-cf53-fc62cd3180f8 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903209073,"timestamp":"2025-09-15T02:26:49.073545+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c9bd67d4-f0a5-257c-cf53-fc62cd3180f8","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757903209073,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:27:49.140Z","event_id":"1904071605210537984","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:27:49.080656+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=721a5fdd-f332-4e91-d9ce-ccd4b05bc187 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903269080,"timestamp":"2025-09-15T02:27:49.080656+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"721a5fdd-f332-4e91-d9ce-ccd4b05bc187","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":752148558,"host.name":"dev-careportal","time":1757903269080,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:28:49.260Z","event_id":"1904071857371705344","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:28:49.199137+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c348f01e-7a27-d417-1c76-cb4af0aac7d4 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903329199,"timestamp":"2025-09-15T02:28:49.199137+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c348f01e-7a27-d417-1c76-cb4af0aac7d4","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64614606,"host.name":"dev-careportal","time":1757903329199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:29:49.175Z","event_id":"1904072108671741952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:29:49.114295+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ce5301f4-6b9b-4ece-ce67-6edfe3a7943b fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903389114,"timestamp":"2025-09-15T02:29:49.114295+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ce5301f4-6b9b-4ece-ce67-6edfe3a7943b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65053479,"host.name":"dev-careportal","time":1757903389114,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:30:00.371Z","event_id":"1904072155632095234","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:30:00.002868+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903400002,"timestamp":"2025-09-15T02:30:00.002868+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65182907,"host.name":"dev-careportal","time":1757903400002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:30:00.374Z","event_id":"1904072155643801619","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:30:00.001078+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903400001,"timestamp":"2025-09-15T02:30:00.001078+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65344521,"host.name":"dev-careportal","time":1757903400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:30:00.764Z","event_id":"1904072157280829440","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:30:00.388062+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903400388,"timestamp":"2025-09-15T02:30:00.388062+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585669382,"host.name":"dev-careportal","time":1757903400388,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:30:00.877Z","event_id":"1904072157754413514","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:30:00.51592+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903400515,"timestamp":"2025-09-15T02:30:00.51592+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877105781,"host.name":"dev-careportal","time":1757903400515,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:30:49.183Z","event_id":"1904072360364462080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:30:49.125603+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=213a2430-5a7c-5398-100c-b4d13143da1f fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903449125,"timestamp":"2025-09-15T02:30:49.125603+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"213a2430-5a7c-5398-100c-b4d13143da1f","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916300108,"host.name":"dev-careportal","time":1757903449125,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:31:49.269Z","event_id":"1904072612383068160","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:31:49.210167+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b981b2a9-393c-3cb7-f034-6889145f7fa1 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903509210,"timestamp":"2025-09-15T02:31:49.210167+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b981b2a9-393c-3cb7-f034-6889145f7fa1","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757903509210,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:32:49.186Z","event_id":"1904072863694675969","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:32:49.124634+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b137b8fa-8306-2cb5-10a1-4581202ab531 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903569124,"timestamp":"2025-09-15T02:32:49.124634+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b137b8fa-8306-2cb5-10a1-4581202ab531","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":877058535,"host.name":"dev-careportal","time":1757903569124,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:33:49.133Z","event_id":"1904073115128590338","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:33:49.074395+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ed939f41-ec16-312a-dcc5-28bb1be307ec fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903629074,"timestamp":"2025-09-15T02:33:49.074395+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ed939f41-ec16-312a-dcc5-28bb1be307ec","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":877058535,"host.name":"dev-careportal","time":1757903629074,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:22.114Z","event_id":"1904073253460340736","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:34:22.05463+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=c54dfc78-a7c9-b1ce-0985-f7413ac8435c fwd=\"************\" dyno=web.1 connect=0ms service=4026239ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903662054,"timestamp":"2025-09-15T02:34:22.05463+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"c54dfc78-a7c9-b1ce-0985-f7413ac8435c","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"4026239ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":4026239},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757903662054,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:22.115Z","event_id":"1904073253466947588","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:34:22.056514+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=893d271e-180a-6e74-b36a-51f8c77a9913 fwd=\"************\" dyno=web.1 connect=0ms service=4027099ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903662056,"timestamp":"2025-09-15T02:34:22.056514+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"893d271e-180a-6e74-b36a-51f8c77a9913","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"4027099ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":4027099},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757903662056,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:22.116Z","event_id":"1904073253469638657","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:34:22.056569+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=09d72007-e8b8-1864-c0e2-6662d40f0ddd fwd=\"************\" dyno=web.1 connect=0ms service=4026839ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903662056,"timestamp":"2025-09-15T02:34:22.056569+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"09d72007-e8b8-1864-c0e2-6662d40f0ddd","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"4026839ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":4026839},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757903662056,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:22.421Z","event_id":"1904073254750220288","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:34:22.055965+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903662055,"timestamp":"2025-09-15T02:34:22.055965+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583892058,"host.name":"dev-careportal","time":1757903662055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:22.421Z","event_id":"1904073254750220289","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:34:22.057444+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903662057,"timestamp":"2025-09-15T02:34:22.057444+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583892058,"host.name":"dev-careportal","time":1757903662057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:22.421Z","event_id":"1904073254750220290","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:34:22.057651+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903662057,"timestamp":"2025-09-15T02:34:22.057651+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583892058,"host.name":"dev-careportal","time":1757903662057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:34:49.274Z","event_id":"1904073367380447232","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:34:49.209827+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=02ebfe4f-3e74-1f82-2e9b-0a2f27690aca fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903689209,"timestamp":"2025-09-15T02:34:49.209827+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"02ebfe4f-3e74-1f82-2e9b-0a2f27690aca","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915624821,"host.name":"dev-careportal","time":1757903689209,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:35:00.366Z","event_id":"1904073413902696448","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:35:00.002539+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903700002,"timestamp":"2025-09-15T02:35:00.002539+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64200766,"host.name":"dev-careportal","time":1757903700002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:35:00.366Z","event_id":"1904073413902696449","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:35:00.248978+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903700248,"timestamp":"2025-09-15T02:35:00.248978+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64200766,"host.name":"dev-careportal","time":1757903700248,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:35:00.366Z","event_id":"1904073413903269888","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:35:00.00068+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903700000,"timestamp":"2025-09-15T02:35:00.00068+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1796594422,"host.name":"dev-careportal","time":1757903700000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:35:00.654Z","event_id":"1904073415108817111","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:35:00.291051+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903700291,"timestamp":"2025-09-15T02:35:00.291051+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":585235856,"host.name":"dev-careportal","time":1757903700291,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:35:49.115Z","event_id":"1904073618371211269","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:35:49.055855+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=314e3e0d-f4fc-74c2-e1ed-2c40ab754d92 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903749055,"timestamp":"2025-09-15T02:35:49.055855+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"314e3e0d-f4fc-74c2-e1ed-2c40ab754d92","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757903749055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:36:47.562Z","event_id":"1904073863513468928","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:36:47.505027+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=8818cb48-0dd9-065f-fb4f-3636304750f1 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903807505,"timestamp":"2025-09-15T02:36:47.505027+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"8818cb48-0dd9-065f-fb4f-3636304750f1","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757903807505,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:36:47.875Z","event_id":"1904073864827445311","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:36:47.505881+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903807505,"timestamp":"2025-09-15T02:36:47.505881+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64048138,"host.name":"dev-careportal","time":1757903807505,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:36:47.995Z","event_id":"1904073865332015106","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:36:47.938726+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=acfd8d50-bd21-ad7f-2a68-80f5cf936228 fwd=\"*************\" dyno=web.1 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903807938,"timestamp":"2025-09-15T02:36:47.938726+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"acfd8d50-bd21-ad7f-2a68-80f5cf936228","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":583427709,"host.name":"dev-careportal","time":1757903807938,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:36:48.302Z","event_id":"1904073866620063746","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:36:47.939439+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757903807939,"timestamp":"2025-09-15T02:36:47.939439+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585948818,"host.name":"dev-careportal","time":1757903807939,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:36:49.100Z","event_id":"1904073869966430209","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:36:49.039459+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cb417de4-5949-4b4a-d54e-7a8a296c3539 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903809039,"timestamp":"2025-09-15T02:36:49.039459+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cb417de4-5949-4b4a-d54e-7a8a296c3539","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757903809039,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:37:49.305Z","event_id":"1904074122483339272","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:37:49.245671+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=eb6f8ba5-45e4-22c2-a4ff-94a3d09949b7 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903869245,"timestamp":"2025-09-15T02:37:49.245671+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"eb6f8ba5-45e4-22c2-a4ff-94a3d09949b7","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757903869245,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:38:49.149Z","event_id":"1904074373486850064","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:38:49.089818+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=7d9eb00d-c9c0-140a-6d6b-fb5448b5213f fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903929089,"timestamp":"2025-09-15T02:38:49.089818+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"7d9eb00d-c9c0-140a-6d6b-fb5448b5213f","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757903929089,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:39:49.118Z","event_id":"1904074625016365057","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:39:49.057199+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4827d7f6-bdd5-f1a2-937d-a3eeb5839563 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757903989057,"timestamp":"2025-09-15T02:39:49.057199+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4827d7f6-bdd5-f1a2-937d-a3eeb5839563","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757903989057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:40:00.363Z","event_id":"1904074672180633602","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:40:00.000711+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904000000,"timestamp":"2025-09-15T02:40:00.000711+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65412522,"host.name":"dev-careportal","time":1757904000000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:40:00.372Z","event_id":"1904074672218382653","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:40:00.005713+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904000005,"timestamp":"2025-09-15T02:40:00.005713+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":916758651,"host.name":"dev-careportal","time":1757904000005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:40:00.642Z","event_id":"1904074673351491584","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:40:00.275315+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904000275,"timestamp":"2025-09-15T02:40:00.275315+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64682922,"host.name":"dev-careportal","time":1757904000275,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:40:00.673Z","event_id":"1904074673481830400","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:40:00.310843+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904000310,"timestamp":"2025-09-15T02:40:00.310843+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":911299866,"host.name":"dev-careportal","time":1757904000310,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:40:49.267Z","event_id":"1904074877298307072","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:40:49.205555+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=316a9ee9-2edf-8b04-33da-c770522428b3 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904049205,"timestamp":"2025-09-15T02:40:49.205555+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"316a9ee9-2edf-8b04-33da-c770522428b3","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757904049205,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:41:49.183Z","event_id":"1904075128606232578","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:41:49.124179+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5a282125-9781-8d21-3455-61c3b07cfd2a fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904109124,"timestamp":"2025-09-15T02:41:49.124179+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5a282125-9781-8d21-3455-61c3b07cfd2a","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":877058535,"host.name":"dev-careportal","time":1757904109124,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:42:49.124Z","event_id":"1904075380016652289","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:42:49.064759+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a9ac247c-79d5-3a7d-95e8-cb8d83355f04 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904169064,"timestamp":"2025-09-15T02:42:49.064759+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a9ac247c-79d5-3a7d-95e8-cb8d83355f04","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585794316,"host.name":"dev-careportal","time":1757904169064,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:43:49.288Z","event_id":"1904075632362356773","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:43:49.230363+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ee7f06f9-b876-f831-0585-bcefb688d229 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904229230,"timestamp":"2025-09-15T02:43:49.230363+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ee7f06f9-b876-f831-0585-bcefb688d229","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387249987,"host.name":"dev-careportal","time":1757904229230,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:44:49.192Z","event_id":"1904075883617570816","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:44:49.13123+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3111f603-b479-e145-1128-e6c81565ea35 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904289131,"timestamp":"2025-09-15T02:44:49.13123+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3111f603-b479-e145-1128-e6c81565ea35","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757904289131,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:45:00.363Z","event_id":"1904075930472480780","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:45:00.001024+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904300001,"timestamp":"2025-09-15T02:45:00.001024+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":873837287,"host.name":"dev-careportal","time":1757904300001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:45:00.363Z","event_id":"1904075930472480781","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:45:00.238273+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904300238,"timestamp":"2025-09-15T02:45:00.238273+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":873837287,"host.name":"dev-careportal","time":1757904300238,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:45:00.376Z","event_id":"1904075930526359556","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:45:00.002217+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904300002,"timestamp":"2025-09-15T02:45:00.002217+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751259511,"host.name":"dev-careportal","time":1757904300002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:45:00.693Z","event_id":"1904075931855335424","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:45:00.322473+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904300322,"timestamp":"2025-09-15T02:45:00.322473+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583089298,"host.name":"dev-careportal","time":1757904300322,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:45:49.173Z","event_id":"1904076135196864512","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:45:49.111721+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ae07d778-a3e0-0621-204e-21dc0d2fbb13 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904349111,"timestamp":"2025-09-15T02:45:49.111721+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ae07d778-a3e0-0621-204e-21dc0d2fbb13","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":918836381,"host.name":"dev-careportal","time":1757904349111,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:46:49.227Z","event_id":"1904076387080544256","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:46:49.170776+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f65a50fa-3c33-2258-071a-239dec0dfe89 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904409170,"timestamp":"2025-09-15T02:46:49.170776+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f65a50fa-3c33-2258-071a-239dec0dfe89","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757904409170,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:47:49.124Z","event_id":"1904076638308229128","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:47:49.063283+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2218a3b1-1226-28d1-7316-14412fa6c39c fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904469063,"timestamp":"2025-09-15T02:47:49.063283+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2218a3b1-1226-28d1-7316-14412fa6c39c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65053479,"host.name":"dev-careportal","time":1757904469063,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:48:49.114Z","event_id":"1904076889924030467","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:48:49.055606+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=692cecb5-0886-6590-0c0f-39a226de32ec fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904529055,"timestamp":"2025-09-15T02:48:49.055606+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"692cecb5-0886-6590-0c0f-39a226de32ec","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387249987,"host.name":"dev-careportal","time":1757904529055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:49:49.330Z","event_id":"1904077142486118402","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:49:49.267532+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=60d6cf67-776e-9391-2285-40bc4f66fcc4 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904589267,"timestamp":"2025-09-15T02:49:49.267532+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"60d6cf67-776e-9391-2285-40bc4f66fcc4","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757904589267,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:50:00.366Z","event_id":"1904077188774457348","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:50:00.001502+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904600001,"timestamp":"2025-09-15T02:50:00.001502+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64447757,"host.name":"dev-careportal","time":1757904600001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:50:00.368Z","event_id":"1904077188784967694","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:50:00.001921+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904600001,"timestamp":"2025-09-15T02:50:00.001921+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64399363,"host.name":"dev-careportal","time":1757904600001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:50:00.711Z","event_id":"1904077190223613955","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:50:00.344293+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904600344,"timestamp":"2025-09-15T02:50:00.344293+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":315784300,"host.name":"dev-careportal","time":1757904600344,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:50:00.713Z","event_id":"1904077190231719938","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:50:00.346452+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904600346,"timestamp":"2025-09-15T02:50:00.346452+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":56582585,"host.name":"dev-careportal","time":1757904600346,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:50:49.152Z","event_id":"1904077393399611392","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:50:49.095201+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9223e280-37e5-5132-bcfa-6a4df09eb05d fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904649095,"timestamp":"2025-09-15T02:50:49.095201+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9223e280-37e5-5132-bcfa-6a4df09eb05d","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757904649095,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:51:49.107Z","event_id":"1904077644868734976","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:51:49.045694+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f2067cc2-4bcf-94b6-ee5e-9cee1ed88cbe fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904709045,"timestamp":"2025-09-15T02:51:49.045694+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f2067cc2-4bcf-94b6-ee5e-9cee1ed88cbe","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757904709045,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:52:49.259Z","event_id":"1904077897165660163","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:52:49.196523+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a90aafdb-a79f-0de2-ed73-3b5223b3b892 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904769196,"timestamp":"2025-09-15T02:52:49.196523+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a90aafdb-a79f-0de2-ed73-3b5223b3b892","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916207073,"host.name":"dev-careportal","time":1757904769196,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:53:49.127Z","event_id":"1904078148268756992","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:53:49.067369+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=33649613-2a45-aa54-7a73-5790b9dec991 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904829067,"timestamp":"2025-09-15T02:53:49.067369+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"33649613-2a45-aa54-7a73-5790b9dec991","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915420555,"host.name":"dev-careportal","time":1757904829067,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:54:49.183Z","event_id":"1904078400163373057","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:54:49.126499+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b5cc7b3b-a549-fc75-2a08-1d7caa58ff39 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904889126,"timestamp":"2025-09-15T02:54:49.126499+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b5cc7b3b-a549-fc75-2a08-1d7caa58ff39","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916207073,"host.name":"dev-careportal","time":1757904889126,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:55:00.369Z","event_id":"1904078447080079366","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:55:00.002868+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904900002,"timestamp":"2025-09-15T02:55:00.002868+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757904900002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:55:00.369Z","event_id":"1904078447080079367","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T02:55:00.244927+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904900244,"timestamp":"2025-09-15T02:55:00.244927+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757904900244,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:55:00.377Z","event_id":"1904078447114006714","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:55:00.002995+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904900002,"timestamp":"2025-09-15T02:55:00.002995+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64425082,"host.name":"dev-careportal","time":1757904900002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:55:00.377Z","event_id":"1904078447114006715","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T02:55:00.244506+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757904900244,"timestamp":"2025-09-15T02:55:00.244506+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64425082,"host.name":"dev-careportal","time":1757904900244,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:55:49.284Z","event_id":"1904078652245430272","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:55:49.221896+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9d0aa0c4-8120-ae02-812e-2ebddcd78aab fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757904949221,"timestamp":"2025-09-15T02:55:49.221896+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9d0aa0c4-8120-ae02-812e-2ebddcd78aab","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757904949221,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:56:49.159Z","event_id":"1904078903378694148","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:56:49.097094+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=defb7d4c-d338-4da0-5fb4-b0dba5cdd278 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905009097,"timestamp":"2025-09-15T02:56:49.097094+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"defb7d4c-d338-4da0-5fb4-b0dba5cdd278","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757905009097,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:57:49.212Z","event_id":"1904079155257651202","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:57:49.150427+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=beb93a75-dc4a-3ded-b9cf-511630f4efa2 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905069150,"timestamp":"2025-09-15T02:57:49.150427+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"beb93a75-dc4a-3ded-b9cf-511630f4efa2","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585709848,"host.name":"dev-careportal","time":1757905069150,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:58:49.288Z","event_id":"1904079407235076096","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:58:49.228521+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1949591d-ae7f-2ac6-7586-e463da55e844 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905129228,"timestamp":"2025-09-15T02:58:49.228521+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1949591d-ae7f-2ac6-7586-e463da55e844","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65353257,"host.name":"dev-careportal","time":1757905129228,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T02:59:49.176Z","event_id":"1904079658424807424","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T02:59:49.118366+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=95062ae3-ffeb-694d-f8e6-5d03b9961368 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905189118,"timestamp":"2025-09-15T02:59:49.118366+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"95062ae3-ffeb-694d-f8e6-5d03b9961368","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757905189118,"source_name":"dev-careportal"}
