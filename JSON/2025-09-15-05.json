{"receive_time":"2025-09-15T05:00:00.372Z","event_id":"1904109904370823320","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:00:00.001559+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912400001,"timestamp":"2025-09-15T05:00:00.001559+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598639734,"host.name":"dev-careportal","time":1757912400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:00:00.373Z","event_id":"1904109904377634816","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:00:00.001941+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912400001,"timestamp":"2025-09-15T05:00:00.001941+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65687472,"host.name":"dev-careportal","time":1757912400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:00:00.654Z","event_id":"1904109905553616897","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:00:00.291369+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912400291,"timestamp":"2025-09-15T05:00:00.291369+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872497930,"host.name":"dev-careportal","time":1757912400291,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:00:00.674Z","event_id":"1904109905639309312","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:00:00.298549+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912400298,"timestamp":"2025-09-15T05:00:00.298549+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64399363,"host.name":"dev-careportal","time":1757912400298,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:00:49.148Z","event_id":"1904110108953153537","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:00:49.087528+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=70a059d0-744e-12db-e477-6375b90ad270 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912449087,"timestamp":"2025-09-15T05:00:49.087528+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"70a059d0-744e-12db-e477-6375b90ad270","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585709848,"host.name":"dev-careportal","time":1757912449087,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:01:49.238Z","event_id":"1904110360988880899","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:01:49.179787+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3800ee31-60f6-f7f3-9180-809919227137 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912509179,"timestamp":"2025-09-15T05:01:49.179787+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3800ee31-60f6-f7f3-9180-809919227137","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583221001,"host.name":"dev-careportal","time":1757912509179,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:02:49.158Z","event_id":"1904110612312313871","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:02:49.097439+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d7c214e1-c803-283b-2b94-c7749142cd70 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912569097,"timestamp":"2025-09-15T05:02:49.097439+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d7c214e1-c803-283b-2b94-c7749142cd70","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583158568,"host.name":"dev-careportal","time":1757912569097,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:03:49.226Z","event_id":"1904110864256577536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:03:49.165924+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b66f9e0f-68df-770b-6cab-746b30b8b02b fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912629165,"timestamp":"2025-09-15T05:03:49.165924+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b66f9e0f-68df-770b-6cab-746b30b8b02b","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":597260972,"host.name":"dev-careportal","time":1757912629165,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:04:49.250Z","event_id":"1904111116013932544","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:04:49.192994+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9620238b-2fd0-3a46-4244-380da8f9f53e fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912689192,"timestamp":"2025-09-15T05:04:49.192994+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9620238b-2fd0-3a46-4244-380da8f9f53e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":597260972,"host.name":"dev-careportal","time":1757912689192,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:05:00.373Z","event_id":"1904111162666807297","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:05:00.005484+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912700005,"timestamp":"2025-09-15T05:05:00.005484+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1264953453,"host.name":"dev-careportal","time":1757912700005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:05:00.373Z","event_id":"1904111162666807298","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:05:00.254184+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912700254,"timestamp":"2025-09-15T05:05:00.254184+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1264953453,"host.name":"dev-careportal","time":1757912700254,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:05:00.380Z","event_id":"1904111162695843840","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:05:00.002254+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912700002,"timestamp":"2025-09-15T05:05:00.002254+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872510944,"host.name":"dev-careportal","time":1757912700002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:05:00.642Z","event_id":"1904111163795075072","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:05:00.279307+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757912700279,"timestamp":"2025-09-15T05:05:00.279307+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64373882,"host.name":"dev-careportal","time":1757912700279,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:05:49.190Z","event_id":"1904111367420096519","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:05:49.129552+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c1b0d1a9-4c05-f743-57dd-315039e537f9 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912749129,"timestamp":"2025-09-15T05:05:49.129552+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c1b0d1a9-4c05-f743-57dd-315039e537f9","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387203154,"host.name":"dev-careportal","time":1757912749129,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:06:49.209Z","event_id":"1904111619158396929","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:06:49.148591+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=df13e915-e5c0-414f-3757-15222b9f9339 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912809148,"timestamp":"2025-09-15T05:06:49.148591+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"df13e915-e5c0-414f-3757-15222b9f9339","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387383325,"host.name":"dev-careportal","time":1757912809148,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:07:49.239Z","event_id":"1904111870942097408","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:07:49.180911+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8715af7e-fb60-296b-4cff-677a23f5a266 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912869180,"timestamp":"2025-09-15T05:07:49.180911+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8715af7e-fb60-296b-4cff-677a23f5a266","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885802123,"host.name":"dev-careportal","time":1757912869180,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:08:49.187Z","event_id":"1904112122383499274","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:08:49.126099+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=fd653afe-0469-9be5-d995-c3563d86c000 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912929126,"timestamp":"2025-09-15T05:08:49.126099+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"fd653afe-0469-9be5-d995-c3563d86c000","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757912929126,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:09:49.201Z","event_id":"1904112374101065730","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:09:49.140853+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1f05fa79-5ec1-242d-b425-42e433358779 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757912989140,"timestamp":"2025-09-15T05:09:49.140853+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1f05fa79-5ec1-242d-b425-42e433358779","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":873925035,"host.name":"dev-careportal","time":1757912989140,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:10:00.381Z","event_id":"1904112420992811008","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:10:00.005559+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913000005,"timestamp":"2025-09-15T05:10:00.005559+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65227763,"host.name":"dev-careportal","time":1757913000005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:10:00.381Z","event_id":"1904112420992811009","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:10:00.208373+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913000208,"timestamp":"2025-09-15T05:10:00.208373+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65227763,"host.name":"dev-careportal","time":1757913000208,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:10:00.388Z","event_id":"1904112421020332032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:10:00.000694+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913000000,"timestamp":"2025-09-15T05:10:00.000694+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915452677,"host.name":"dev-careportal","time":1757913000000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:10:00.691Z","event_id":"1904112422292328449","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:10:00.316858+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913000316,"timestamp":"2025-09-15T05:10:00.316858+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":2927710912,"host.name":"dev-careportal","time":1757913000316,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:10:49.266Z","event_id":"1904112626031329280","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:10:49.20874+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=402ed190-fc79-6822-a2ea-a85cecf4c2e5 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913049208,"timestamp":"2025-09-15T05:10:49.20874+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"402ed190-fc79-6822-a2ea-a85cecf4c2e5","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583047165,"host.name":"dev-careportal","time":1757913049208,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:11:49.123Z","event_id":"1904112877090099200","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:11:49.061526+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=06bc4adf-d192-e2da-8b8f-f06879c63129 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913109061,"timestamp":"2025-09-15T05:11:49.061526+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"06bc4adf-d192-e2da-8b8f-f06879c63129","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757913109061,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:12:49.187Z","event_id":"1904113129016893445","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:12:49.108784+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f3b2cd3e-a554-49fa-0cd3-6c4d08f6a735 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913169108,"timestamp":"2025-09-15T05:12:49.108784+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f3b2cd3e-a554-49fa-0cd3-6c4d08f6a735","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":875415171,"host.name":"dev-careportal","time":1757913169108,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:13:49.235Z","event_id":"1904113380876816384","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:13:49.175839+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2cc9fca2-ce2e-3816-4ec4-04814e901d93 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913229175,"timestamp":"2025-09-15T05:13:49.175839+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2cc9fca2-ce2e-3816-4ec4-04814e901d93","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885657101,"host.name":"dev-careportal","time":1757913229175,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:14:49.149Z","event_id":"1904113632172871681","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:14:49.090523+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d54cec63-8090-d51a-5334-d533b5e5f3f8 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913289090,"timestamp":"2025-09-15T05:14:49.090523+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d54cec63-8090-d51a-5334-d533b5e5f3f8","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915624821,"host.name":"dev-careportal","time":1757913289090,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:15:00.377Z","event_id":"1904113679267901440","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:15:00.001253+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913300001,"timestamp":"2025-09-15T05:15:00.001253+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":921472316,"host.name":"dev-careportal","time":1757913300001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:15:00.381Z","event_id":"1904113679283130371","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:15:00.006095+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913300006,"timestamp":"2025-09-15T05:15:00.006095+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583366555,"host.name":"dev-careportal","time":1757913300006,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:15:00.751Z","event_id":"1904113680835530754","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:15:00.386333+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913300386,"timestamp":"2025-09-15T05:15:00.386333+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583725831,"host.name":"dev-careportal","time":1757913300386,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:15:00.757Z","event_id":"1904113680861069320","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:15:00.393535+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913300393,"timestamp":"2025-09-15T05:15:00.393535+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757913300393,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:15:49.195Z","event_id":"1904113884025434112","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:15:49.138105+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1770de56-8db1-2ffb-8cc9-814bc480d6de fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913349138,"timestamp":"2025-09-15T05:15:49.138105+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1770de56-8db1-2ffb-8cc9-814bc480d6de","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585794316,"host.name":"dev-careportal","time":1757913349138,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:16:49.252Z","event_id":"1904114135921938432","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:16:49.188741+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=244ebeb5-e006-a875-c101-3759a70106c5 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913409188,"timestamp":"2025-09-15T05:16:49.188741+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"244ebeb5-e006-a875-c101-3759a70106c5","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":918830047,"host.name":"dev-careportal","time":1757913409188,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:17:49.203Z","event_id":"1904114387373854737","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:17:49.142997+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=fc7a44c5-05f3-c22c-799c-885f23439e39 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913469142,"timestamp":"2025-09-15T05:17:49.142997+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"fc7a44c5-05f3-c22c-799c-885f23439e39","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598322888,"host.name":"dev-careportal","time":1757913469142,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:18:45.142Z","event_id":"1904114621999042566","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:18:45.081765+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=c1dfffa1-baeb-44ca-dc40-34b34d0b3951 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913525081,"timestamp":"2025-09-15T05:18:45.081765+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"c1dfffa1-baeb-44ca-dc40-34b34d0b3951","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":751770311,"host.name":"dev-careportal","time":1757913525081,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:18:45.451Z","event_id":"1904114623294713858","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:18:45.082505+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913525082,"timestamp":"2025-09-15T05:18:45.082505+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64541584,"host.name":"dev-careportal","time":1757913525082,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:18:49.201Z","event_id":"1904114639023403009","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:18:49.141269+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=7fe14b0d-c49c-8332-6e55-2c87ee8595a9 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913529141,"timestamp":"2025-09-15T05:18:49.141269+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"7fe14b0d-c49c-8332-6e55-2c87ee8595a9","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":3091819137,"host.name":"dev-careportal","time":1757913529141,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:19:49.269Z","event_id":"1904114890967158793","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:19:49.21213+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=157a760d-9bb3-72bc-a78b-b8d74fa05cfd fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913589212,"timestamp":"2025-09-15T05:19:49.21213+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"157a760d-9bb3-72bc-a78b-b8d74fa05cfd","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757913589212,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:20:00.366Z","event_id":"1904114937512296454","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:20:00.001761+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913600001,"timestamp":"2025-09-15T05:20:00.001761+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585653277,"host.name":"dev-careportal","time":1757913600001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:20:00.385Z","event_id":"1904114937591955457","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:20:00.001528+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913600001,"timestamp":"2025-09-15T05:20:00.001528+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":911694378,"host.name":"dev-careportal","time":1757913600001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:20:00.631Z","event_id":"1904114938622537734","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:20:00.269807+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913600269,"timestamp":"2025-09-15T05:20:00.269807+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":1679314106,"host.name":"dev-careportal","time":1757913600269,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:20:00.635Z","event_id":"1904114938639314947","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:20:00.271227+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913600271,"timestamp":"2025-09-15T05:20:00.271227+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":316010271,"host.name":"dev-careportal","time":1757913600271,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:20:49.160Z","event_id":"1904115142169923604","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:20:49.100123+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c5862935-e07f-83d2-ddc3-53db38f52c6f fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913649100,"timestamp":"2025-09-15T05:20:49.100123+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c5862935-e07f-83d2-ddc3-53db38f52c6f","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":873925035,"host.name":"dev-careportal","time":1757913649100,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:21:49.129Z","event_id":"1904115393697771521","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:21:49.068083+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4c481d02-261b-0c23-8b88-8a22bcd297a0 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913709068,"timestamp":"2025-09-15T05:21:49.068083+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4c481d02-261b-0c23-8b88-8a22bcd297a0","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583158568,"host.name":"dev-careportal","time":1757913709068,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:22:49.252Z","event_id":"1904115645871923200","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:22:49.191062+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1a76bc9b-292a-e804-7030-9484f1ca2a84 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913769191,"timestamp":"2025-09-15T05:22:49.191062+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1a76bc9b-292a-e804-7030-9484f1ca2a84","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757913769191,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:23:49.172Z","event_id":"1904115897192919041","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:23:49.111319+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=685821c7-7617-806b-f1d3-058ab06c4a93 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913829111,"timestamp":"2025-09-15T05:23:49.111319+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"685821c7-7617-806b-f1d3-058ab06c4a93","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":877223021,"host.name":"dev-careportal","time":1757913829111,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:24:49.153Z","event_id":"1904116148771192832","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:24:49.092925+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ac64d032-0e6f-729a-6e1e-dd9b4018db9a fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913889092,"timestamp":"2025-09-15T05:24:49.092925+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ac64d032-0e6f-729a-6e1e-dd9b4018db9a","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":873925035,"host.name":"dev-careportal","time":1757913889092,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:25:00.365Z","event_id":"1904116195798003712","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:25:00.001367+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913900001,"timestamp":"2025-09-15T05:25:00.001367+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64190022,"host.name":"dev-careportal","time":1757913900001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:25:00.375Z","event_id":"1904116195840528384","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:25:00.002953+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913900002,"timestamp":"2025-09-15T05:25:00.002953+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":877246267,"host.name":"dev-careportal","time":1757913900002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:25:00.648Z","event_id":"1904116196987068416","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:25:00.281881+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913900281,"timestamp":"2025-09-15T05:25:00.281881+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":872508094,"host.name":"dev-careportal","time":1757913900281,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:25:00.659Z","event_id":"1904116197030854656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:25:00.297814+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757913900297,"timestamp":"2025-09-15T05:25:00.297814+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":911299866,"host.name":"dev-careportal","time":1757913900297,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:25:49.287Z","event_id":"1904116400994013184","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:25:49.226271+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4a29893f-2f40-a75b-d22d-2b7197620d58 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757913949226,"timestamp":"2025-09-15T05:25:49.226271+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4a29893f-2f40-a75b-d22d-2b7197620d58","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":752413774,"host.name":"dev-careportal","time":1757913949226,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:26:49.126Z","event_id":"1904116651976970242","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:26:49.069769+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c0f18804-ee49-777c-9bd9-ea7399055ac1 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914009069,"timestamp":"2025-09-15T05:26:49.069769+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c0f18804-ee49-777c-9bd9-ea7399055ac1","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915635062,"host.name":"dev-careportal","time":1757914009069,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:27:49.242Z","event_id":"1904116904119205889","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:27:49.186509+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=775202bd-c95d-241d-883f-f395e7dfd08e fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914069186,"timestamp":"2025-09-15T05:27:49.186509+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"775202bd-c95d-241d-883f-f395e7dfd08e","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757914069186,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:28:49.297Z","event_id":"1904117156009988097","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:28:49.233371+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0e94969f-23a0-6d6f-3746-96afeb3dc059 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914129233,"timestamp":"2025-09-15T05:28:49.233371+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0e94969f-23a0-6d6f-3746-96afeb3dc059","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583221001,"host.name":"dev-careportal","time":1757914129233,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:29:49.179Z","event_id":"1904117407173406720","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:29:49.115988+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6796e146-70e6-cfe1-5ba9-33a380923705 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914189115,"timestamp":"2025-09-15T05:29:49.115988+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6796e146-70e6-cfe1-5ba9-33a380923705","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598626444,"host.name":"dev-careportal","time":1757914189115,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:30:00.376Z","event_id":"1904117454135758851","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:30:00.001941+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914200001,"timestamp":"2025-09-15T05:30:00.001941+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64425082,"host.name":"dev-careportal","time":1757914200001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:30:00.376Z","event_id":"1904117454135758852","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:30:00.005509+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914200005,"timestamp":"2025-09-15T05:30:00.005509+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64425082,"host.name":"dev-careportal","time":1757914200005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:30:00.376Z","event_id":"1904117454135758853","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:30:00.224628+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914200224,"timestamp":"2025-09-15T05:30:00.224628+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64425082,"host.name":"dev-careportal","time":1757914200224,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:30:00.705Z","event_id":"1904117455516532736","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:30:00.338738+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914200338,"timestamp":"2025-09-15T05:30:00.338738+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64943925,"host.name":"dev-careportal","time":1757914200338,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:30:49.254Z","event_id":"1904117659145457680","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:30:49.191813+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=11e65245-8aa2-deeb-17e0-af0d1fd391d8 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914249191,"timestamp":"2025-09-15T05:30:49.191813+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"11e65245-8aa2-deeb-17e0-af0d1fd391d8","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583168653,"host.name":"dev-careportal","time":1757914249191,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:31:49.230Z","event_id":"1904117910702526465","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:31:49.172823+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9b2cfd3c-e7b2-071e-8597-273fa7cc3157 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914309172,"timestamp":"2025-09-15T05:31:49.172823+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9b2cfd3c-e7b2-071e-8597-273fa7cc3157","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757914309172,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:32:49.123Z","event_id":"1904118161912823808","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:32:49.06634+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=463e7ca1-b96e-aa23-dd87-d46b44071a04 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914369066,"timestamp":"2025-09-15T05:32:49.06634+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"463e7ca1-b96e-aa23-dd87-d46b44071a04","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387195821,"host.name":"dev-careportal","time":1757914369066,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:33:49.273Z","event_id":"1904118414198403072","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:33:49.211486+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=22027247-d21d-26ee-be36-d49b494069e8 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914429211,"timestamp":"2025-09-15T05:33:49.211486+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"22027247-d21d-26ee-be36-d49b494069e8","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":752560270,"host.name":"dev-careportal","time":1757914429211,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:34:33.784Z","event_id":"1904118600893480960","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:34:33.721296+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=0f88fe8b-d0dd-b6bd-e949-28ce96291308 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914473721,"timestamp":"2025-09-15T05:34:33.721296+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"0f88fe8b-d0dd-b6bd-e949-28ce96291308","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":3091819137,"host.name":"dev-careportal","time":1757914473721,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:34:34.088Z","event_id":"1904118602168946688","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:34:33.721917+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914473721,"timestamp":"2025-09-15T05:34:33.721917+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315834133,"host.name":"dev-careportal","time":1757914473721,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:34:49.253Z","event_id":"1904118665773297664","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:34:49.191887+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d53940d1-38ba-d289-020d-ef195d63681a fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914489191,"timestamp":"2025-09-15T05:34:49.191887+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d53940d1-38ba-d289-020d-ef195d63681a","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598626444,"host.name":"dev-careportal","time":1757914489191,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:35:00.372Z","event_id":"1904118712411840513","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:35:00.001854+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914500001,"timestamp":"2025-09-15T05:35:00.001854+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585948818,"host.name":"dev-careportal","time":1757914500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:35:00.375Z","event_id":"1904118712424017932","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:35:00.005501+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914500005,"timestamp":"2025-09-15T05:35:00.005501+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":3091775304,"host.name":"dev-careportal","time":1757914500005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:35:00.632Z","event_id":"1904118713500864512","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:35:00.26515+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914500265,"timestamp":"2025-09-15T05:35:00.26515+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598432550,"host.name":"dev-careportal","time":1757914500265,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:35:00.637Z","event_id":"1904118713522941952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:35:00.2703+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914500270,"timestamp":"2025-09-15T05:35:00.2703+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":876024762,"host.name":"dev-careportal","time":1757914500270,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:35:49.119Z","event_id":"1904118916871770112","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:35:49.061999+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=155e1de8-953e-84aa-bdc7-ecd065cd2472 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914549061,"timestamp":"2025-09-15T05:35:49.061999+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"155e1de8-953e-84aa-bdc7-ecd065cd2472","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757914549061,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:36:49.248Z","event_id":"1904119169069731840","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:36:49.185941+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c9346b7d-078a-599a-28a2-62294e6d0e81 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914609185,"timestamp":"2025-09-15T05:36:49.185941+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c9346b7d-078a-599a-28a2-62294e6d0e81","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65549438,"host.name":"dev-careportal","time":1757914609185,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:37:49.335Z","event_id":"1904119421093249024","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:37:49.27491+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8cb5af49-fb2f-9346-14c7-433565992c0b fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914669274,"timestamp":"2025-09-15T05:37:49.27491+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8cb5af49-fb2f-9346-14c7-433565992c0b","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64030460,"host.name":"dev-careportal","time":1757914669274,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:38:49.157Z","event_id":"1904119672004902912","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:38:49.099928+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=49042779-86a7-4f94-5ba9-76335ce0a08c fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914729099,"timestamp":"2025-09-15T05:38:49.099928+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"49042779-86a7-4f94-5ba9-76335ce0a08c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872759260,"host.name":"dev-careportal","time":1757914729099,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:39:49.145Z","event_id":"1904119923613384707","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:39:49.086503+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6f987969-09d0-e4e0-fa63-5ec5643574ab fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914789086,"timestamp":"2025-09-15T05:39:49.086503+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6f987969-09d0-e4e0-fa63-5ec5643574ab","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751280573,"host.name":"dev-careportal","time":1757914789086,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:40:00.366Z","event_id":"1904119970677469359","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:40:00.002194+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914800002,"timestamp":"2025-09-15T05:40:00.002194+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916247599,"host.name":"dev-careportal","time":1757914800002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:40:00.366Z","event_id":"1904119970677469360","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:40:00.232829+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914800232,"timestamp":"2025-09-15T05:40:00.232829+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916247599,"host.name":"dev-careportal","time":1757914800232,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:40:00.388Z","event_id":"1904119970770149376","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:40:00.001791+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914800001,"timestamp":"2025-09-15T05:40:00.001791+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":586139717,"host.name":"dev-careportal","time":1757914800001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:40:00.388Z","event_id":"1904119970770149377","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:40:00.213789+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757914800213,"timestamp":"2025-09-15T05:40:00.213789+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":586139717,"host.name":"dev-careportal","time":1757914800213,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:40:49.263Z","event_id":"1904120175766757382","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:40:49.206292+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=34878b0a-c807-db2b-bb32-3e22871ebfbc fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914849206,"timestamp":"2025-09-15T05:40:49.206292+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"34878b0a-c807-db2b-bb32-3e22871ebfbc","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598626444,"host.name":"dev-careportal","time":1757914849206,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:41:49.152Z","event_id":"1904120426958618624","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:41:49.090591+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6bfe0f99-25e5-8165-4d3a-9c5d5010114f fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914909090,"timestamp":"2025-09-15T05:41:49.090591+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6bfe0f99-25e5-8165-4d3a-9c5d5010114f","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757914909090,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:42:49.276Z","event_id":"1904120679137955843","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:42:49.216265+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e4bd05e5-203f-c07e-57f2-1a35a31703f1 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757914969216,"timestamp":"2025-09-15T05:42:49.216265+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e4bd05e5-203f-c07e-57f2-1a35a31703f1","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583168653,"host.name":"dev-careportal","time":1757914969216,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:43:49.343Z","event_id":"1904120931075526656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:43:49.277752+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=431bd261-aba6-6794-2747-1f4425552313 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915029277,"timestamp":"2025-09-15T05:43:49.277752+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"431bd261-aba6-6794-2747-1f4425552313","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757915029277,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:44:49.137Z","event_id":"1904121181868617728","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:44:49.075989+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bcb9e419-1cea-1b5a-04bb-1d95588de16a fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915089075,"timestamp":"2025-09-15T05:44:49.075989+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bcb9e419-1cea-1b5a-04bb-1d95588de16a","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583168653,"host.name":"dev-careportal","time":1757915089075,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:45:00.368Z","event_id":"1904121228976652288","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:45:00.001479+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915100001,"timestamp":"2025-09-15T05:45:00.001479+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":872730734,"host.name":"dev-careportal","time":1757915100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:45:00.368Z","event_id":"1904121228976652289","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:45:00.246596+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915100246,"timestamp":"2025-09-15T05:45:00.246596+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":872730734,"host.name":"dev-careportal","time":1757915100246,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:45:00.388Z","event_id":"1904121229060198400","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:45:00.00117+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915100001,"timestamp":"2025-09-15T05:45:00.00117+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64588467,"host.name":"dev-careportal","time":1757915100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:45:00.668Z","event_id":"1904121230234259457","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:45:00.294078+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915100294,"timestamp":"2025-09-15T05:45:00.294078+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583341211,"host.name":"dev-careportal","time":1757915100294,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:45:49.181Z","event_id":"1904121433713819648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:45:49.121213+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=fe4420f7-1344-21a1-0ac3-1548790205a0 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915149121,"timestamp":"2025-09-15T05:45:49.121213+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"fe4420f7-1344-21a1-0ac3-1548790205a0","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885915710,"host.name":"dev-careportal","time":1757915149121,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:46:49.273Z","event_id":"1904121685757362176","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:46:49.212048+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=92b15c67-304b-c10a-3597-8ca2337f3c94 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915209212,"timestamp":"2025-09-15T05:46:49.212048+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"92b15c67-304b-c10a-3597-8ca2337f3c94","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872749197,"host.name":"dev-careportal","time":1757915209212,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:47:49.185Z","event_id":"1904121937044930561","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:47:49.125546+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2f9e39a4-f31e-9611-7825-a1d1de51389e fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915269125,"timestamp":"2025-09-15T05:47:49.125546+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2f9e39a4-f31e-9611-7825-a1d1de51389e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":918830047,"host.name":"dev-careportal","time":1757915269125,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:48:49.228Z","event_id":"1904122188885856258","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:48:49.168467+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0b08cd73-64ed-a015-2143-f1f2bb6d5b76 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915329168,"timestamp":"2025-09-15T05:48:49.168467+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0b08cd73-64ed-a015-2143-f1f2bb6d5b76","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757915329168,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:49:41.550Z","event_id":"1904122408340140034","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:49:41.48699+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=6b17ec90-bb9f-2fea-c88d-ed13d5d38511 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915381486,"timestamp":"2025-09-15T05:49:41.48699+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"6b17ec90-bb9f-2fea-c88d-ed13d5d38511","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":583221001,"host.name":"dev-careportal","time":1757915381486,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:49:42.856Z","event_id":"1904122413817806849","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:49:41.487609+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915381487,"timestamp":"2025-09-15T05:49:41.487609+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":911299866,"host.name":"dev-careportal","time":1757915381487,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:49:49.195Z","event_id":"1904122440405594112","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:49:49.138393+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0f0d03a5-1c70-0019-df7b-bc4cab427767 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915389138,"timestamp":"2025-09-15T05:49:49.138393+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0f0d03a5-1c70-0019-df7b-bc4cab427767","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757915389138,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:50:00.367Z","event_id":"1904122487264448512","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:50:00.001506+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915400001,"timestamp":"2025-09-15T05:50:00.001506+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315834133,"host.name":"dev-careportal","time":1757915400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:50:00.370Z","event_id":"1904122487276556298","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:50:00.002996+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915400002,"timestamp":"2025-09-15T05:50:00.002996+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":56582585,"host.name":"dev-careportal","time":1757915400002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:50:00.631Z","event_id":"1904122488371654656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:50:00.268576+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915400268,"timestamp":"2025-09-15T05:50:00.268576+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":751014183,"host.name":"dev-careportal","time":1757915400268,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:50:00.666Z","event_id":"1904122488516538368","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:50:00.291343+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915400291,"timestamp":"2025-09-15T05:50:00.291343+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877246267,"host.name":"dev-careportal","time":1757915400291,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:50:49.150Z","event_id":"1904122691875180544","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:50:49.092229+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=31c95649-8999-a7d9-dfab-a1b3e42b5b8c fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915449092,"timestamp":"2025-09-15T05:50:49.092229+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"31c95649-8999-a7d9-dfab-a1b3e42b5b8c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387195821,"host.name":"dev-careportal","time":1757915449092,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:51:49.153Z","event_id":"1904122943543672833","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:51:49.096649+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3f8b5860-8e1f-389e-c405-3e6197ec32d5 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915509096,"timestamp":"2025-09-15T05:51:49.096649+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3f8b5860-8e1f-389e-c405-3e6197ec32d5","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757915509096,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:52:17.808Z","event_id":"1904123063732654080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:52:17.441976+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915537441,"timestamp":"2025-09-15T05:52:17.441976+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64048138,"host.name":"dev-careportal","time":1757915537441,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:52:17.808Z","event_id":"1904123063732654081","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:52:17.442459+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915537442,"timestamp":"2025-09-15T05:52:17.442459+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64048138,"host.name":"dev-careportal","time":1757915537442,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:52:17.808Z","event_id":"1904123063732654082","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:52:17.532208+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915537532,"timestamp":"2025-09-15T05:52:17.532208+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64048138,"host.name":"dev-careportal","time":1757915537532,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:52:20.080Z","event_id":"1904123073262452875","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:52:19.713774+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915539713,"timestamp":"2025-09-15T05:52:19.713774+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585759616,"host.name":"dev-careportal","time":1757915539713,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:52:49.259Z","event_id":"1904123195646509059","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:52:49.19782+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9ef008de-824d-91ae-cef2-38d487beb21c fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915569197,"timestamp":"2025-09-15T05:52:49.19782+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9ef008de-824d-91ae-cef2-38d487beb21c","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583222442,"host.name":"dev-careportal","time":1757915569197,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:53:49.117Z","event_id":"1904123446711701507","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:53:49.055839+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8e3561b8-2afc-afbb-dbc6-4d7e6a4f737a fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915629055,"timestamp":"2025-09-15T05:53:49.055839+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8e3561b8-2afc-afbb-dbc6-4d7e6a4f737a","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585674247,"host.name":"dev-careportal","time":1757915629055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:54:49.265Z","event_id":"1904123698990010368","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:54:49.206256+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9e560750-fa0f-0c95-efa1-b4fe2aa1da6f fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915689206,"timestamp":"2025-09-15T05:54:49.206256+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9e560750-fa0f-0c95-efa1-b4fe2aa1da6f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583740246,"host.name":"dev-careportal","time":1757915689206,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:55:00.372Z","event_id":"1904123745576529933","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:55:00.005497+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915700005,"timestamp":"2025-09-15T05:55:00.005497+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65687472,"host.name":"dev-careportal","time":1757915700005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:55:00.375Z","event_id":"1904123745587728479","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:55:00.003268+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915700003,"timestamp":"2025-09-15T05:55:00.003268+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":873837287,"host.name":"dev-careportal","time":1757915700003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:55:00.632Z","event_id":"1904123746665082880","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T05:55:00.266507+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915700266,"timestamp":"2025-09-15T05:55:00.266507+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":2927703590,"host.name":"dev-careportal","time":1757915700266,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:55:00.643Z","event_id":"1904123746711638016","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T05:55:00.276837+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757915700276,"timestamp":"2025-09-15T05:55:00.276837+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65057852,"host.name":"dev-careportal","time":1757915700276,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:55:49.246Z","event_id":"1904123950567026689","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:55:49.185718+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9b6518be-3abe-77fa-a485-e021b6685d99 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915749185,"timestamp":"2025-09-15T05:55:49.185718+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9b6518be-3abe-77fa-a485-e021b6685d99","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915635062,"host.name":"dev-careportal","time":1757915749185,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:56:49.144Z","event_id":"1904124201798664193","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:56:49.088077+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=56267b32-abbd-6a8c-cc80-c45eb39b8428 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915809088,"timestamp":"2025-09-15T05:56:49.088077+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"56267b32-abbd-6a8c-cc80-c45eb39b8428","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583952165,"host.name":"dev-careportal","time":1757915809088,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:57:49.224Z","event_id":"1904124453791764480","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:57:49.166963+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c7a5d764-d8ce-b33f-6c6b-be5ecc7def12 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915869166,"timestamp":"2025-09-15T05:57:49.166963+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c7a5d764-d8ce-b33f-6c6b-be5ecc7def12","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387195821,"host.name":"dev-careportal","time":1757915869166,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:58:49.257Z","event_id":"1904124705587294209","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:58:49.193424+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=93477273-9779-4b96-874f-65535337297b fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915929193,"timestamp":"2025-09-15T05:58:49.193424+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"93477273-9779-4b96-874f-65535337297b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757915929193,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T05:59:49.147Z","event_id":"1904124956786388993","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T05:59:49.087117+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9a832bf4-7a99-1d9f-5f62-4da1ec0aebba fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757915989087,"timestamp":"2025-09-15T05:59:49.087117+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9a832bf4-7a99-1d9f-5f62-4da1ec0aebba","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751902213,"host.name":"dev-careportal","time":1757915989087,"source_name":"dev-careportal"}
