{"receive_time":"2025-09-15T03:00:00.365Z","event_id":"1904079705355472897","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:00:00.002954+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905200002,"timestamp":"2025-09-15T03:00:00.002954+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751193404,"host.name":"dev-careportal","time":1757905200002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:00:00.368Z","event_id":"1904079705367367738","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:00:00.00215+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905200002,"timestamp":"2025-09-15T03:00:00.00215+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916634565,"host.name":"dev-careportal","time":1757905200002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:00:00.731Z","event_id":"1904079706889617409","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:00:00.356942+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905200356,"timestamp":"2025-09-15T03:00:00.356942+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751264138,"host.name":"dev-careportal","time":1757905200356,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:00:00.745Z","event_id":"1904079706948911104","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:00:00.360422+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905200360,"timestamp":"2025-09-15T03:00:00.360422+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872730734,"host.name":"dev-careportal","time":1757905200360,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:00:49.190Z","event_id":"1904079910140514789","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:00:49.133138+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e8d5fb04-63dd-b74d-99bd-160f1913436c fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905249133,"timestamp":"2025-09-15T03:00:49.133138+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e8d5fb04-63dd-b74d-99bd-160f1913436c","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757905249133,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:01:49.309Z","event_id":"1904080162298040321","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:01:49.248786+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=674e653c-af4b-ce5d-96d2-7de33a8f17a5 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905309248,"timestamp":"2025-09-15T03:01:49.248786+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"674e653c-af4b-ce5d-96d2-7de33a8f17a5","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916300108,"host.name":"dev-careportal","time":1757905309248,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:02:49.208Z","event_id":"1904080413533372416","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:02:49.151929+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=67dc68ae-5bd5-56b1-b9b0-12b0fd6cdefe fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905369151,"timestamp":"2025-09-15T03:02:49.151929+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"67dc68ae-5bd5-56b1-b9b0-12b0fd6cdefe","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":56511184,"host.name":"dev-careportal","time":1757905369151,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:03:49.112Z","event_id":"1904080664787120129","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:03:49.050508+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=be45dd0d-ccc8-2371-c031-05a1980ce522 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905429050,"timestamp":"2025-09-15T03:03:49.050508+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"be45dd0d-ccc8-2371-c031-05a1980ce522","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64083029,"host.name":"dev-careportal","time":1757905429050,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:04:49.261Z","event_id":"1904080917072539648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:04:49.195736+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4377d2dc-26a1-7ca0-0e29-ce70417bbb99 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905489195,"timestamp":"2025-09-15T03:04:49.195736+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4377d2dc-26a1-7ca0-0e29-ce70417bbb99","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757905489195,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:05:00.364Z","event_id":"1904080963641897465","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:05:00.000867+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905500000,"timestamp":"2025-09-15T03:05:00.000867+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":387340868,"host.name":"dev-careportal","time":1757905500000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:05:00.371Z","event_id":"1904080963670151168","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:05:00.001548+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905500001,"timestamp":"2025-09-15T03:05:00.001548+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757905500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:05:00.657Z","event_id":"1904080964868599808","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:05:00.295734+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905500295,"timestamp":"2025-09-15T03:05:00.295734+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65344521,"host.name":"dev-careportal","time":1757905500295,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:05:00.697Z","event_id":"1904080965036961792","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:05:00.321637+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905500321,"timestamp":"2025-09-15T03:05:00.321637+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":911595259,"host.name":"dev-careportal","time":1757905500321,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:05:49.124Z","event_id":"1904081168156172289","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:05:49.063335+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5b7f9681-9908-cd6d-2809-d499298e5f04 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905549063,"timestamp":"2025-09-15T03:05:49.063335+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5b7f9681-9908-cd6d-2809-d499298e5f04","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":877058535,"host.name":"dev-careportal","time":1757905549063,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:06:49.202Z","event_id":"1904081420139327489","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:06:49.146065+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f2d1c6d8-1599-46b4-ba02-caec1d436a51 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905609146,"timestamp":"2025-09-15T03:06:49.146065+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f2d1c6d8-1599-46b4-ba02-caec1d436a51","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64548717,"host.name":"dev-careportal","time":1757905609146,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:07:49.311Z","event_id":"1904081672255868928","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:07:49.249476+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=7bb4c206-630e-2b95-4b68-7114fa97d8d6 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905669249,"timestamp":"2025-09-15T03:07:49.249476+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"7bb4c206-630e-2b95-4b68-7114fa97d8d6","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":839938714,"host.name":"dev-careportal","time":1757905669249,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:08:49.161Z","event_id":"1904081923286081536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:08:49.10411+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c2f8c817-78e6-9d17-fc3a-78fab2c0aec0 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905729104,"timestamp":"2025-09-15T03:08:49.10411+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c2f8c817-78e6-9d17-fc3a-78fab2c0aec0","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751902213,"host.name":"dev-careportal","time":1757905729104,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:09:49.128Z","event_id":"1904082174804791341","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:09:49.071742+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2d0ae932-3196-ed94-fcb0-00000c3a467c fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905789071,"timestamp":"2025-09-15T03:09:49.071742+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2d0ae932-3196-ed94-fcb0-00000c3a467c","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916296964,"host.name":"dev-careportal","time":1757905789071,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:10:00.372Z","event_id":"1904082221966635013","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:10:00.002108+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905800002,"timestamp":"2025-09-15T03:10:00.002108+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":316052645,"host.name":"dev-careportal","time":1757905800002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:10:00.372Z","event_id":"1904082221966635014","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:10:00.242504+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905800242,"timestamp":"2025-09-15T03:10:00.242504+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":316052645,"host.name":"dev-careportal","time":1757905800242,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:10:00.396Z","event_id":"1904082222066925571","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*********","sw.remote.ip":"*********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:10:00.001498+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905800001,"timestamp":"2025-09-15T03:10:00.001498+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64555014,"host.name":"dev-careportal","time":1757905800001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:10:00.652Z","event_id":"1904082223140667392","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:10:00.28359+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757905800283,"timestamp":"2025-09-15T03:10:00.28359+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":2927703590,"host.name":"dev-careportal","time":1757905800283,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:10:49.273Z","event_id":"1904082427072204801","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:10:49.212773+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6ce2a61e-133e-b634-15c9-56b7745dbebb fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905849212,"timestamp":"2025-09-15T03:10:49.212773+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6ce2a61e-133e-b634-15c9-56b7745dbebb","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757905849212,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:11:49.200Z","event_id":"1904082678424379392","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:11:49.13935+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=72bc6c00-ab06-acd1-85d7-2349efd4d34c fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905909139,"timestamp":"2025-09-15T03:11:49.13935+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"72bc6c00-ab06-acd1-85d7-2349efd4d34c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583427709,"host.name":"dev-careportal","time":1757905909139,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:12:49.114Z","event_id":"1904082929721880578","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:12:49.05749+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9338153e-61b5-d018-0aec-41bf65bf5d54 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757905969057,"timestamp":"2025-09-15T03:12:49.05749+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9338153e-61b5-d018-0aec-41bf65bf5d54","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387271481,"host.name":"dev-careportal","time":1757905969057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:13:49.276Z","event_id":"1904083182058545206","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:13:49.213437+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f23e84ea-b4ae-b7e2-6392-88f7040e2728 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906029213,"timestamp":"2025-09-15T03:13:49.213437+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f23e84ea-b4ae-b7e2-6392-88f7040e2728","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757906029213,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:14:49.143Z","event_id":"1904083433160290313","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:14:49.079495+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=821bda00-5bc4-ba99-8630-ab4c5bddd83c fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906089079,"timestamp":"2025-09-15T03:14:49.079495+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"821bda00-5bc4-ba99-8630-ab4c5bddd83c","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598629640,"host.name":"dev-careportal","time":1757906089079,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:15:00.375Z","event_id":"1904083480268206080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:15:00.006055+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906100006,"timestamp":"2025-09-15T03:15:00.006055+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":876028546,"host.name":"dev-careportal","time":1757906100006,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:15:00.384Z","event_id":"1904083480307761155","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:15:00.001518+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906100001,"timestamp":"2025-09-15T03:15:00.001518+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":585200453,"host.name":"dev-careportal","time":1757906100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:15:00.640Z","event_id":"1904083481380818944","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:15:00.270662+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906100270,"timestamp":"2025-09-15T03:15:00.270662+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916613287,"host.name":"dev-careportal","time":1757906100270,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:15:00.661Z","event_id":"1904083481468899329","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:15:00.298921+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906100298,"timestamp":"2025-09-15T03:15:00.298921+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751241915,"host.name":"dev-careportal","time":1757906100298,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:15:49.180Z","event_id":"1904083684971212801","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:15:49.120357+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e7449ae9-88f6-cc01-e15f-7e4ef077a6a1 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906149120,"timestamp":"2025-09-15T03:15:49.120357+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e7449ae9-88f6-cc01-e15f-7e4ef077a6a1","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916300108,"host.name":"dev-careportal","time":1757906149120,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:16:49.311Z","event_id":"1904083937181147139","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:16:49.249809+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=dbd16182-cda9-40d2-45b1-3d893296208e fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906209249,"timestamp":"2025-09-15T03:16:49.249809+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"dbd16182-cda9-40d2-45b1-3d893296208e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916650686,"host.name":"dev-careportal","time":1757906209249,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:17:49.138Z","event_id":"1904084188113653760","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:17:49.07801+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b386f441-a043-7ea1-cab9-6be99997ecfa fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906269078,"timestamp":"2025-09-15T03:17:49.07801+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b386f441-a043-7ea1-cab9-6be99997ecfa","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757906269078,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:18:49.104Z","event_id":"1904084439628325082","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:18:49.046728+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c4989f35-bdcc-7aca-494a-8c7adebe835e fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906329046,"timestamp":"2025-09-15T03:18:49.046728+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c4989f35-bdcc-7aca-494a-8c7adebe835e","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757906329046,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:19:49.290Z","event_id":"1904084692067012608","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:19:49.229051+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4b4e0f50-ea18-ad78-870a-24d3cde8cd52 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906389229,"timestamp":"2025-09-15T03:19:49.229051+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4b4e0f50-ea18-ad78-870a-24d3cde8cd52","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757906389229,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:20:00.365Z","event_id":"1904084738518929411","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:20:00.00277+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906400002,"timestamp":"2025-09-15T03:20:00.00277+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915452677,"host.name":"dev-careportal","time":1757906400002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:20:00.365Z","event_id":"1904084738518929412","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:20:00.220385+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906400220,"timestamp":"2025-09-15T03:20:00.220385+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915452677,"host.name":"dev-careportal","time":1757906400220,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:20:00.392Z","event_id":"1904084738631667713","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:20:00.002829+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906400002,"timestamp":"2025-09-15T03:20:00.002829+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751241915,"host.name":"dev-careportal","time":1757906400002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:20:00.677Z","event_id":"1904084739826085910","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:20:00.304639+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906400304,"timestamp":"2025-09-15T03:20:00.304639+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64384602,"host.name":"dev-careportal","time":1757906400304,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:20:49.160Z","event_id":"1904084943179993089","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:20:49.101075+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=55e18847-8869-8fd3-9365-84b8feea8141 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906449101,"timestamp":"2025-09-15T03:20:49.101075+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"55e18847-8869-8fd3-9365-84b8feea8141","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757906449101,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:21:49.127Z","event_id":"1904085194698895360","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:21:49.06661+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2aef546a-3dbf-5cd2-95f4-b4ddced25852 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906509066,"timestamp":"2025-09-15T03:21:49.06661+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2aef546a-3dbf-5cd2-95f4-b4ddced25852","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757906509066,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:22:49.255Z","event_id":"1904085446894624773","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:22:49.187012+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=64a43ff0-8bc6-0f3d-2efe-017dd7863764 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906569187,"timestamp":"2025-09-15T03:22:49.187012+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"64a43ff0-8bc6-0f3d-2efe-017dd7863764","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":752148558,"host.name":"dev-careportal","time":1757906569187,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:23:49.125Z","event_id":"1904085698008686592","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:23:49.062234+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=89832b7b-9f2b-66de-5a81-ca9195e0fb7b fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906629062,"timestamp":"2025-09-15T03:23:49.062234+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"89832b7b-9f2b-66de-5a81-ca9195e0fb7b","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598629640,"host.name":"dev-careportal","time":1757906629062,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:24:49.110Z","event_id":"1904085949604184064","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:24:49.049855+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=eff22ab9-9e75-76a0-2b93-f3ea44d56586 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906689049,"timestamp":"2025-09-15T03:24:49.049855+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"eff22ab9-9e75-76a0-2b93-f3ea44d56586","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915578983,"host.name":"dev-careportal","time":1757906689049,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:25:00.369Z","event_id":"1904085996826599428","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:25:00.003644+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906700003,"timestamp":"2025-09-15T03:25:00.003644+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":878105080,"host.name":"dev-careportal","time":1757906700003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:25:00.369Z","event_id":"1904085996826599429","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:25:00.248425+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906700248,"timestamp":"2025-09-15T03:25:00.248425+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":878105080,"host.name":"dev-careportal","time":1757906700248,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:25:00.378Z","event_id":"1904085996865400839","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:25:00.003721+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906700003,"timestamp":"2025-09-15T03:25:00.003721+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872585263,"host.name":"dev-careportal","time":1757906700003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:25:00.658Z","event_id":"1904085998039060480","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:25:00.292145+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757906700292,"timestamp":"2025-09-15T03:25:00.292145+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916283955,"host.name":"dev-careportal","time":1757906700292,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:25:49.255Z","event_id":"1904086201869344770","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:25:49.198244+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=419684f1-04a3-c65d-5d9d-e38c302897ba fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906749198,"timestamp":"2025-09-15T03:25:49.198244+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"419684f1-04a3-c65d-5d9d-e38c302897ba","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757906749198,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:26:49.176Z","event_id":"1904086453195075584","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:26:49.116214+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=72b07d08-0c72-1099-859b-ea4f0255a8b6 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906809116,"timestamp":"2025-09-15T03:26:49.116214+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"72b07d08-0c72-1099-859b-ea4f0255a8b6","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":877058535,"host.name":"dev-careportal","time":1757906809116,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:27:49.114Z","event_id":"1904086704595480577","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:27:49.056469+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5473e87a-2617-3098-55ac-18f8565d5984 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906869056,"timestamp":"2025-09-15T03:27:49.056469+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5473e87a-2617-3098-55ac-18f8565d5984","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757906869056,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:28:49.249Z","event_id":"1904086956819980296","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:28:49.190421+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0422ff16-6e85-9fef-faef-759fe64af9f2 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906929190,"timestamp":"2025-09-15T03:28:49.190421+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0422ff16-6e85-9fef-faef-759fe64af9f2","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64723131,"host.name":"dev-careportal","time":1757906929190,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:29:49.135Z","event_id":"1904087207998951424","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:29:49.073894+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e3b52c19-db33-5e7e-b96c-4d5e29b99740 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757906989073,"timestamp":"2025-09-15T03:29:49.073894+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e3b52c19-db33-5e7e-b96c-4d5e29b99740","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757906989073,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:30:00.371Z","event_id":"1904087255127625728","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:30:00.00039+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907000000,"timestamp":"2025-09-15T03:30:00.00039+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":597254522,"host.name":"dev-careportal","time":1757907000000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:30:00.401Z","event_id":"1904087255252017153","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:30:00.001646+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907000001,"timestamp":"2025-09-15T03:30:00.001646+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":840023651,"host.name":"dev-careportal","time":1757907000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:30:00.618Z","event_id":"1904087256162181120","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:30:00.252615+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907000252,"timestamp":"2025-09-15T03:30:00.252615+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915657614,"host.name":"dev-careportal","time":1757907000252,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:30:00.644Z","event_id":"1904087256272691210","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:30:00.273571+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907000273,"timestamp":"2025-09-15T03:30:00.273571+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":911355445,"host.name":"dev-careportal","time":1757907000273,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:30:49.108Z","event_id":"1904087459542822921","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:30:49.051095+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ed8be426-1e39-12e8-6f99-9d0f5156f7ab fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907049051,"timestamp":"2025-09-15T03:30:49.051095+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ed8be426-1e39-12e8-6f99-9d0f5156f7ab","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64083029,"host.name":"dev-careportal","time":1757907049051,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:31:49.249Z","event_id":"1904087711794671623","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:31:49.186265+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=88d80ac6-7ef5-9005-3cc4-fb5eafb3031e fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907109186,"timestamp":"2025-09-15T03:31:49.186265+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"88d80ac6-7ef5-9005-3cc4-fb5eafb3031e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757907109186,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:32:49.173Z","event_id":"1904087963133399040","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:32:49.111272+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=eecdb99b-2a26-34d4-9230-aa5e4be818ff fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907169111,"timestamp":"2025-09-15T03:32:49.111272+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"eecdb99b-2a26-34d4-9230-aa5e4be818ff","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583420853,"host.name":"dev-careportal","time":1757907169111,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:33:49.122Z","event_id":"1904088214577729553","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:33:49.06392+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=01777b05-d9f3-4258-a24f-9febab16a46b fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907229063,"timestamp":"2025-09-15T03:33:49.06392+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"01777b05-d9f3-4258-a24f-9febab16a46b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64812738,"host.name":"dev-careportal","time":1757907229063,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:34:49.281Z","event_id":"1904088466903904256","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:34:49.219431+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=73b9eefa-6940-a943-f8fe-54f35ea1e6a9 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907289219,"timestamp":"2025-09-15T03:34:49.219431+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"73b9eefa-6940-a943-f8fe-54f35ea1e6a9","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757907289219,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:35:00.368Z","event_id":"1904088513405112320","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:35:00.004462+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907300004,"timestamp":"2025-09-15T03:35:00.004462+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":387272011,"host.name":"dev-careportal","time":1757907300004,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:35:00.585Z","event_id":"1904088514314719233","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:35:00.002597+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907300002,"timestamp":"2025-09-15T03:35:00.002597+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583725831,"host.name":"dev-careportal","time":1757907300002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:35:00.645Z","event_id":"1904088514565468167","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:35:00.2781+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907300278,"timestamp":"2025-09-15T03:35:00.2781+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65182907,"host.name":"dev-careportal","time":1757907300278,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:35:00.666Z","event_id":"1904088514654457857","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:35:00.302147+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907300302,"timestamp":"2025-09-15T03:35:00.302147+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885876564,"host.name":"dev-careportal","time":1757907300302,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:35:49.211Z","event_id":"1904088718267842561","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:35:49.148817+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bfff4f99-7747-1465-9163-a8861838379a fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907349148,"timestamp":"2025-09-15T03:35:49.148817+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bfff4f99-7747-1465-9163-a8861838379a","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757907349148,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:36:49.223Z","event_id":"1904088969976074241","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:36:49.165993+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6137e7ac-2962-8faf-42e3-599223e1dd62 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907409165,"timestamp":"2025-09-15T03:36:49.165993+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6137e7ac-2962-8faf-42e3-599223e1dd62","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872749197,"host.name":"dev-careportal","time":1757907409165,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:37:49.353Z","event_id":"1904089222179913730","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:37:49.297135+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cae5ef65-9d8d-b548-c167-e5d27b1dca71 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907469297,"timestamp":"2025-09-15T03:37:49.297135+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cae5ef65-9d8d-b548-c167-e5d27b1dca71","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757907469297,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:38:49.169Z","event_id":"1904089473066807300","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:38:49.110632+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4ed15eb0-c6da-06a7-f7fd-70b6f86344d0 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907529110,"timestamp":"2025-09-15T03:38:49.110632+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4ed15eb0-c6da-06a7-f7fd-70b6f86344d0","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757907529110,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:39:49.139Z","event_id":"1904089724599418881","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:39:49.075329+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=97106247-939b-5a55-9a1d-d7bcd032e248 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907589075,"timestamp":"2025-09-15T03:39:49.075329+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"97106247-939b-5a55-9a1d-d7bcd032e248","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757907589075,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:40:00.384Z","event_id":"1904089771764076545","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:40:00.003932+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907600003,"timestamp":"2025-09-15T03:40:00.003932+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":750780615,"host.name":"dev-careportal","time":1757907600003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:40:00.384Z","event_id":"1904089771764076546","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:40:00.231313+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907600231,"timestamp":"2025-09-15T03:40:00.231313+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":750780615,"host.name":"dev-careportal","time":1757907600231,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:40:00.384Z","event_id":"1904089771764183054","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:40:00.002327+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907600002,"timestamp":"2025-09-15T03:40:00.002327+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1679314106,"host.name":"dev-careportal","time":1757907600002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:40:00.660Z","event_id":"1904089772922200065","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:40:00.288147+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907600288,"timestamp":"2025-09-15T03:40:00.288147+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64399363,"host.name":"dev-careportal","time":1757907600288,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:40:49.266Z","event_id":"1904089976788881408","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:40:49.206157+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=637ac5fc-ea6d-0ff1-475d-d0a9583ddaf8 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907649206,"timestamp":"2025-09-15T03:40:49.206157+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"637ac5fc-ea6d-0ff1-475d-d0a9583ddaf8","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585401146,"host.name":"dev-careportal","time":1757907649206,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:41:49.157Z","event_id":"1904090227991212032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:41:49.099501+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c2d7731c-fd41-8c72-721c-f09450d38fad fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907709099,"timestamp":"2025-09-15T03:41:49.099501+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c2d7731c-fd41-8c72-721c-f09450d38fad","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757907709099,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:42:49.280Z","event_id":"1904090480164929536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:42:49.219701+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=adeb6a8b-006f-0f76-df98-c7e69039d7a1 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907769219,"timestamp":"2025-09-15T03:42:49.219701+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"adeb6a8b-006f-0f76-df98-c7e69039d7a1","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":315444933,"host.name":"dev-careportal","time":1757907769219,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:43:49.262Z","event_id":"1904090731747672064","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:43:49.205549+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ca9721a3-013d-a19e-ccb6-ea5fb0a9c797 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907829205,"timestamp":"2025-09-15T03:43:49.205549+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ca9721a3-013d-a19e-ccb6-ea5fb0a9c797","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":315444933,"host.name":"dev-careportal","time":1757907829205,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:44:49.179Z","event_id":"1904090983058788626","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:44:49.118526+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ac948581-3925-184d-5905-6eb573b2827e fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907889118,"timestamp":"2025-09-15T03:44:49.118526+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ac948581-3925-184d-5905-6eb573b2827e","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585339536,"host.name":"dev-careportal","time":1757907889118,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:45:00.368Z","event_id":"1904091029987004416","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:45:00.006308+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907900006,"timestamp":"2025-09-15T03:45:00.006308+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885913596,"host.name":"dev-careportal","time":1757907900006,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:45:00.450Z","event_id":"1904091030332190721","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:45:00.001789+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907900001,"timestamp":"2025-09-15T03:45:00.001789+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1264953453,"host.name":"dev-careportal","time":1757907900001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:45:00.714Z","event_id":"1904091031438741505","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:45:00.348567+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907900348,"timestamp":"2025-09-15T03:45:00.348567+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583299955,"host.name":"dev-careportal","time":1757907900348,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:45:00.758Z","event_id":"1904091031622733824","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:45:00.39022+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757907900390,"timestamp":"2025-09-15T03:45:00.39022+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":875970981,"host.name":"dev-careportal","time":1757907900390,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:45:49.221Z","event_id":"1904091234891501568","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:45:49.160138+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=627c1c2f-b911-c651-8f80-03d6d6fe563a fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757907949160,"timestamp":"2025-09-15T03:45:49.160138+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"627c1c2f-b911-c651-8f80-03d6d6fe563a","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387195821,"host.name":"dev-careportal","time":1757907949160,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:46:49.259Z","event_id":"1904091486709841920","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:46:49.195935+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=79072ac2-8700-3359-58d1-5a42d4c364f6 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908009195,"timestamp":"2025-09-15T03:46:49.195935+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"79072ac2-8700-3359-58d1-5a42d4c364f6","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915574842,"host.name":"dev-careportal","time":1757908009195,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:47:49.119Z","event_id":"1904091737779998720","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:47:49.058098+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=fff9bc15-a03c-c8c7-eb00-0a194138e001 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908069058,"timestamp":"2025-09-15T03:47:49.058098+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"fff9bc15-a03c-c8c7-eb00-0a194138e001","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598405177,"host.name":"dev-careportal","time":1757908069058,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:48:49.156Z","event_id":"1904091989594308608","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:48:49.099109+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=27bab415-9489-e351-31b3-5744c736c8c4 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908129099,"timestamp":"2025-09-15T03:48:49.099109+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"27bab415-9489-e351-31b3-5744c736c8c4","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872686091,"host.name":"dev-careportal","time":1757908129099,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:49:49.291Z","event_id":"1904092241818779648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:49:49.232255+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=22aaffa8-04ce-0fe5-20ca-0f2c8261eea3 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908189232,"timestamp":"2025-09-15T03:49:49.232255+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"22aaffa8-04ce-0fe5-20ca-0f2c8261eea3","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916300108,"host.name":"dev-careportal","time":1757908189232,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:50:00.366Z","event_id":"1904092288270323723","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:50:00.00246+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908200002,"timestamp":"2025-09-15T03:50:00.00246+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872585263,"host.name":"dev-careportal","time":1757908200002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:50:00.368Z","event_id":"1904092288277786632","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:50:00.002133+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908200002,"timestamp":"2025-09-15T03:50:00.002133+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64425082,"host.name":"dev-careportal","time":1757908200002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:50:00.626Z","event_id":"1904092289361993728","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:50:00.258328+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908200258,"timestamp":"2025-09-15T03:50:00.258328+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872707604,"host.name":"dev-careportal","time":1757908200258,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:50:00.671Z","event_id":"1904092289550360577","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:50:00.296772+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908200296,"timestamp":"2025-09-15T03:50:00.296772+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":876028546,"host.name":"dev-careportal","time":1757908200296,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:50:49.182Z","event_id":"1904092493020229641","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:50:49.122214+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4c7db5e5-76b4-f592-6e19-1710efd69978 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908249122,"timestamp":"2025-09-15T03:50:49.122214+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4c7db5e5-76b4-f592-6e19-1710efd69978","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585401146,"host.name":"dev-careportal","time":1757908249122,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:51:49.173Z","event_id":"1904092744640999424","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:51:49.111677+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6f6b6cc3-eadb-f040-c9ee-9934ccffa03e fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908309111,"timestamp":"2025-09-15T03:51:49.111677+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6f6b6cc3-eadb-f040-c9ee-9934ccffa03e","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757908309111,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:52:49.270Z","event_id":"1904092996706197716","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:52:49.211808+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c6102481-1010-2f6e-df99-89db13938137 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908369211,"timestamp":"2025-09-15T03:52:49.211808+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c6102481-1010-2f6e-df99-89db13938137","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65380042,"host.name":"dev-careportal","time":1757908369211,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:53:49.129Z","event_id":"1904093247771545600","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:53:49.067952+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3783489e-6691-b709-c04b-55b3fe264fad fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908429067,"timestamp":"2025-09-15T03:53:49.067952+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3783489e-6691-b709-c04b-55b3fe264fad","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585709848,"host.name":"dev-careportal","time":1757908429067,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:54:49.176Z","event_id":"1904093499627634688","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:54:49.114401+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9616e050-2a8d-8b09-5fc0-d5b2192c17d4 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908489114,"timestamp":"2025-09-15T03:54:49.114401+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9616e050-2a8d-8b09-5fc0-d5b2192c17d4","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757908489114,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:55:00.372Z","event_id":"1904093546587635717","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:55:00.001393+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908500001,"timestamp":"2025-09-15T03:55:00.001393+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583709310,"host.name":"dev-careportal","time":1757908500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:55:00.385Z","event_id":"1904093546640908291","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:55:00.001082+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908500001,"timestamp":"2025-09-15T03:55:00.001082+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598639734,"host.name":"dev-careportal","time":1757908500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:55:00.645Z","event_id":"1904093547732496385","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T03:55:00.27925+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908500279,"timestamp":"2025-09-15T03:55:00.27925+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":921472316,"host.name":"dev-careportal","time":1757908500279,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:55:00.666Z","event_id":"1904093547818889216","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T03:55:00.299372+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757908500299,"timestamp":"2025-09-15T03:55:00.299372+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":876024762,"host.name":"dev-careportal","time":1757908500299,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:55:49.239Z","event_id":"1904093751548276740","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:55:49.177584+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bdb0002d-384f-7ae6-4786-47da979a22ee fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908549177,"timestamp":"2025-09-15T03:55:49.177584+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bdb0002d-384f-7ae6-4786-47da979a22ee","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915420555,"host.name":"dev-careportal","time":1757908549177,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:56:49.157Z","event_id":"1904094002865393664","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:56:49.096077+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f1882e0e-a1de-0008-97a1-544a5bf3ee1f fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908609096,"timestamp":"2025-09-15T03:56:49.096077+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f1882e0e-a1de-0008-97a1-544a5bf3ee1f","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64548717,"host.name":"dev-careportal","time":1757908609096,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:57:49.181Z","event_id":"1904094254623993858","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:57:49.119102+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=36fb1120-5886-5f41-08cb-79258f0c3d33 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908669119,"timestamp":"2025-09-15T03:57:49.119102+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"36fb1120-5886-5f41-08cb-79258f0c3d33","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872759260,"host.name":"dev-careportal","time":1757908669119,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:58:49.241Z","event_id":"1904094506533625856","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:58:49.180735+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ca0fb3cc-ab51-e15e-d9f7-35664bc70506 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908729180,"timestamp":"2025-09-15T03:58:49.180735+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ca0fb3cc-ab51-e15e-d9f7-35664bc70506","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585219483,"host.name":"dev-careportal","time":1757908729180,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T03:59:49.223Z","event_id":"1904094758115934208","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T03:59:49.156023+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0afa3309-a617-6b99-fd77-30422efb07f1 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757908789156,"timestamp":"2025-09-15T03:59:49.156023+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0afa3309-a617-6b99-fd77-30422efb07f1","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757908789156,"source_name":"dev-careportal"}
